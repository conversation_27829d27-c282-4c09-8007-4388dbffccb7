<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="
		http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans.xsd
		http://camel.apache.org/schema/spring
		http://camel.apache.org/schema/spring/camel-spring-2.16.1.xsd">
	<bean class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
		<property name="locations">
			<list>
				<value>classpath:/application-test.properties</value>
			</list>
		</property>
	</bean>
	<!-- Needed for Camel Context properties -->
	<bean id="properties" class="org.apache.camel.component.properties.PropertiesComponent">
		<property name="location" value="classpath:/application-test.properties" />
	</bean>

	<bean id="opacFailureProcessor" class="com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.OPACFailureProcessor" />

	<bean id="opacBusinessRulesValidator" class="com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.OPACFileBusinessRulesValidator" />

	<bean id="router" class="com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.routing.Router" />

	
	<bean id="sapMapper" class="com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.mapping.EigenToSapMapper">
		<constructor-arg type="java.lang.String" value="4711" />
	</bean>

	<bean id="sapDeclinedMapper" class="com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.mapping.EigenToSapDeclinedMapper">
		<constructor-arg type="java.lang.String" value="4711" />
	</bean>

	<camelContext id="ERP.Subscriber.Payment.OPACFileConverter.camel" xmlns="http://camel.apache.org/schema/spring" autoStartup="true" errorHandlerRef="errorHandler" streamCache="true">

		<errorHandler id="errorHandler" type="DeadLetterChannel" deadLetterUri="direct:deadLetterQueue" useOriginalMessage="true" onPrepareFailureRef="opacFailureProcessor">
			<redeliveryPolicy maximumRedeliveries="0" retryAttemptedLogLevel="INFO" />
		</errorHandler>

		<dataFormats>
			<beanio id="sapDataFormat" mapping="beanio-mappings.sap.xml" streamName="sapData" encoding="UTF-8" />
			<beanio id="sapDeclinedDataFormat" mapping="beanio-mappings.sap.declined.xml" streamName="SAPDeclinedData" encoding="UTF-8" />
			<beanio id="eigenDataFormat" mapping="beanio-mappings.eigen.xml" streamName="eigenData" encoding="UTF-8" />
		</dataFormats>

		<!-- Below is need due a bug in Camel. The file is not moved in case of error otherwise. -->
		<onException useOriginalMessage="true">
			<exception>java.lang.Exception</exception>
			<to uri="direct:deadLetterQueue" />
		</onException>

		<!-- generic test -->
		<route id="ERP.Subscriber.Payment.OPACFileConverter.test.generic">
			<from uri="seda:genericStart" />
			<unmarshal ref="eigenDataFormat" />
			<to uri="mock:genericEnd" />
		</route>




		<route id="ERPSubscriber.Payment.OPACFileConverter.test.sapMapper">
			<from uri="direct:sapMapperTest" />
			<unmarshal ref="eigenDataFormat" />
			<process ref="sapMapper" />
			<marshal ref="sapDataFormat" />
			<to uri="mock:endSapMapperTest" />
		</route>

		<route id="ERPSubscriber.Payment.OPACFileConverter.test.sapDeclinedMapper">
			<from uri="direct:sapDeclinedMapperTest" />
			<unmarshal ref="eigenDataFormat" />
			<process ref="sapDeclinedMapper" />
			<marshal ref="sapDeclinedDataFormat" />
			<to uri="mock:endSapDeclinedMapperTest" />
		</route>


		<!-- Eigen file validator test -->
		<route id="ERP.Subscriber.Payment.OPACFileConverter.test.validator">
			<from uri="seda:startOpacValidatorTest" />
			<unmarshal ref="eigenDataFormat" />
			<process ref="opacBusinessRulesValidator" />
			<to uri="mock:validatorOpacTestResult" />
		</route>

		<!-- Eigen router test -->
		<route id="ERP.Subscriber.Payment.OPACFileConverter.test.router">
			<from uri="direct:routerTest" />
			<unmarshal ref="eigenDataFormat" />
			<process ref="router" />
			<to uri="mock:endRouterTest" />
		</route>

		<route id="ERP.Subscriber.Payment.OPACFileConverter.test.dlq">
			<from uri="direct:deadLetterQueue" />
			<log message="{{log.errorTag}}-dlq-${exception.stacktrace}." loggingLevel="ERROR" />
			<to uri="mock:deadLetterQueue" />
		</route>
	</camelContext>
</beans>
