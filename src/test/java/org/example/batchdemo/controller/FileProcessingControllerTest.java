package org.example.batchdemo.controller;

import org.example.batchdemo.service.FileProcessingService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(FileProcessingController.class)
class FileProcessingControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private FileProcessingService fileProcessingService;

    @Test
    void testFetchFiles() throws Exception {
        when(fileProcessingService.startFileFetch()).thenReturn("File fetching initiated");

        mockMvc.perform(post("/api/opac/fetch")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.message").value("File fetching initiated"));
    }

    @Test
    void testStartProcessing() throws Exception {
        when(fileProcessingService.startFileProcessing()).thenReturn("File processing initiated successfully");

        mockMvc.perform(post("/api/opac/router")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.message").value("File processing initiated successfully"));
    }

    @Test
    void testValidateFile() throws Exception {
        when(fileProcessingService.validateFile()).thenReturn("File validation initiated");

        mockMvc.perform(post("/api/opac/validate")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.message").value("File validation initiated"));
    }
}
