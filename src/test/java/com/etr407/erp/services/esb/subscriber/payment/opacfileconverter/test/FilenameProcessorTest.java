package com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.test;

import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.FilenameProcessor;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.exception.MissingFilenameException;
import org.apache.camel.CamelContext;
import org.apache.camel.Exchange;
import org.apache.camel.impl.DefaultCamelContext;
import org.apache.camel.impl.DefaultExchange;
import org.apache.camel.test.spring.CamelSpringTestSupport;
import org.junit.Before;
import org.junit.Test;
import org.springframework.context.support.AbstractApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;


public class FilenameProcessorTest extends CamelSpringTestSupport {
	FilenameProcessor fp;
	CamelContext stubContext;
	Exchange stubExchange;

	@Override
	protected AbstractApplicationContext createApplicationContext() {
		return new ClassPathXmlApplicationContext("camel-context-test.xml");
	}

	@Before
	public void init() {
		fp = new FilenameProcessor();
		stubContext = new DefaultCamelContext();
		stubExchange = new DefaultExchange(stubContext);
		
	}

	@Test
	public void testValidContentDispositionHeader() throws MissingFilenameException {
		stubExchange.getIn().setHeader("Content-Disposition", "attachment; filename=\"opac_request_20210401.txt\"");
		fp.process(stubExchange);
		assertEquals("opac_request_20210401.txt", stubExchange.getIn().getHeader("CamelFileName"));
	}

	@Test
	public void testNullContentDispositionHeader() {
		stubExchange.getIn().setHeader("Content-Disposition", null);
		try {
			fp.process(stubExchange);
			fail("Expected a MissingFilenameException");
		} catch (MissingFilenameException e) {
			assertEquals("No Content-Disposition header found", e.getMessage());
		}
	}

	@Test
	public void testInvalidContentDispositionHeader() {
		stubExchange.getIn().setHeader("Content-Disposition", "");
		try {
			fp.process(stubExchange);
			fail("Expected a MissingFilenameException");
		} catch (MissingFilenameException e) {
			assertEquals("No valid filename found in the Content-Disposition header", e.getMessage());
		}
	}

	@Test
	public void testInvalidFilename() {
		stubExchange.getIn().setHeader("Content-Disposition", "filename=\"\"");
		try {
			fp.process(stubExchange);
			fail("Expected a MissingFilenameException");
		} catch (MissingFilenameException e) {
			assertEquals("No valid filename found in the Content-Disposition header", e.getMessage());
		}
	}

	@Test
	public void testMissingContentDispositionHeader() {
		FilenameProcessor fp = new FilenameProcessor();
		try {
			fp.process(stubExchange);
			fail("Expected a MissingFilenameException");
		} catch (MissingFilenameException e) {
			assertEquals("No Content-Disposition header found", e.getMessage());
		}
	}

}
