package com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.mapping.test;


import java.io.InputStream;
import java.text.ParseException;

import org.apache.camel.Exchange;
import org.apache.camel.component.mock.MockEndpoint;
import org.apache.camel.impl.DefaultCamelContext;
import org.apache.camel.impl.DefaultExchange;
import org.apache.camel.test.spring.CamelSpringTestSupport;
import org.junit.Before;
import org.junit.Test;
import org.springframework.context.support.AbstractApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.exception.ValidationException;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.mapping.EigenToSapDeclinedMapper;

public class EigenToSapDeclinedMapperTest extends CamelSpringTestSupport {

	private Exchange stubExchange;
	private EigenToSapDeclinedMapper mapper;
	private static final int ASSERT_PERIOD = 50;

	@Override
	protected AbstractApplicationContext createApplicationContext() {
		return new ClassPathXmlApplicationContext("camel-context-test.xml");
	}

	@Before
	public void init() {
		stubExchange = new DefaultExchange(new DefaultCamelContext());
		mapper = new EigenToSapDeclinedMapper("210");
	}

	@Test
	public void testMapperInCamel() throws InterruptedException {
		InputStream bodyStream = this.getClass().getResourceAsStream("/data/input.eigen.sapDeclinedMapping.txt");
		String body = context.getTypeConverter().convertTo(String.class, bodyStream);
		body = body.replaceAll("\\r\\n", "\n");
		body = body.replaceAll("\\r", "\n");
		stubExchange.getIn().setBody(body);
		stubExchange.setProperty("processDate", "20210122");

		template.send("direct:sapDeclinedMapperTest", stubExchange);

		MockEndpoint resultEndpoint = context.getEndpoint("mock:endSapDeclinedMapperTest", MockEndpoint.class);
		resultEndpoint.expectedMessageCount(1);
		resultEndpoint.setAssertPeriod(ASSERT_PERIOD);
		resultEndpoint.assertIsSatisfied();

		Exchange end = resultEndpoint.getExchanges().get(0);
		String returnedBody = end.getIn().getBody(String.class);
		assertEqualsResource("/data/out.sapDeclined.gold", returnedBody);
	}

	@Test
	public void testMapperNullBody() throws ValidationException, ParseException {
		mapper.process(stubExchange);
		assertNull(stubExchange.getIn().getBody());
	}

	/**
	 * Helper method for comparing a string against a classpath resource.
	 * @param goldFileResourcePath expected file result
	 * @param testString actual result string
	 */
	void assertEqualsResource(String goldFileResourcePath, String testString) {
		String goldString = context
				.getTypeConverter()
				.convertTo(String.class, this.getClass().getResourceAsStream(goldFileResourcePath));
		// cleanse line terminators
		goldString = goldString.replaceAll("\\r\\n?", "\n");
		testString = testString.replaceAll("\\r\\n?", "\n");
		assertEquals(goldString, testString);
	}
}
