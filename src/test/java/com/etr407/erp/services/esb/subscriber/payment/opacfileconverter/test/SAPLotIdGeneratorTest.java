package com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.test;

import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.SAPLotIdGenerator;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenRootGroup;
import org.apache.camel.CamelContext;
import org.apache.camel.Exchange;
import org.apache.camel.impl.DefaultCamelContext;
import org.apache.camel.impl.DefaultExchange;
import org.apache.camel.model.dataformat.BeanioDataFormat;
import org.apache.camel.spring.SpringCamelContext;
import org.apache.camel.test.spring.CamelSpringTestSupport;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.springframework.context.support.AbstractXmlApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import java.io.InputStream;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

public class SAPLotIdGeneratorTest extends CamelSpringTestSupport {
	static final String PROCESS_DATE_STRING = "19851026";
	private SpringCamelContext camel;

	CamelContext stubContext;
	Exchange stubExchange;

	@Override
	protected AbstractXmlApplicationContext createApplicationContext() {
		ClassPathXmlApplicationContext classPathXmlApplicationContext = new ClassPathXmlApplicationContext("camel-context-test.xml");
		camel = (SpringCamelContext) classPathXmlApplicationContext.getBean("ERP.Subscriber.Payment.OPACFileConverter.camel");
		return classPathXmlApplicationContext;
	}

	@Before
	public void init() {
		stubContext = new DefaultCamelContext();
		stubExchange = new DefaultExchange(stubContext);
	}

	@After
	public void tearDown() {
	}

	/**
	 * Test that a new (first) file has the correct SAP lot ID.
	 *
	 * @throws ParseException
	 */
	@Test
	public void testValidSapLotId() throws Exception {
		Exchange exchange = new DefaultExchange(camel);
		BeanioDataFormat format = (BeanioDataFormat) exchange.getContext().getDataFormats().get("eigenDataFormat");
		InputStream data = this.getClass().getResourceAsStream("/data/input.eigen.sapMapping.txt");
		List<EigenRootGroup> body = (ArrayList<EigenRootGroup>) format.getDataFormat().unmarshal(exchange, data);
		exchange.getIn().setBody(body);

		exchange.setProperty("nextSeqNum", 1);
		exchange.setProperty("processDate", PROCESS_DATE_STRING);
		SAPLotIdGenerator lip = new SAPLotIdGenerator();
		lip.process(exchange);
		assertEquals("MCOA85102601", exchange.getIn().getHeader("sapLotId"));
	}

	/**
	 * Test lot Id generation when its value is Zero
	 * @throws ParseException
	 */
	@Test
	public void testSapLotId_ZeroValue() throws Exception {
		Exchange exchange = new DefaultExchange(camel);
		BeanioDataFormat format = (BeanioDataFormat) exchange.getContext().getDataFormats().get("eigenDataFormat");
		InputStream data = this.getClass().getResourceAsStream("/data/input.eigen.sapMapping.txt");
		List<EigenRootGroup> body = (ArrayList<EigenRootGroup>) format.getDataFormat().unmarshal(exchange, data);
		exchange.getIn().setBody(body);
		//exchange.getIn().setHeader("sapLotIdSuffixNum", 0);
		exchange.setProperty("nextSeqNum", 0);
		exchange.setProperty("processDate", PROCESS_DATE_STRING);
		SAPLotIdGenerator lip = new SAPLotIdGenerator();
		lip.process(exchange);
		assertEquals("MCOA85102600", exchange.getIn().getHeader("sapLotId"));
	}

	/**
	 * Test lot Id generation when the Process date is Invalid
	 *
	 * @throws ParseException
	 */
	@Test(expected = ParseException.class)
	public void testSapLotId_InvalidDate() throws Exception {
		//stubExchange.getIn().setHeader("sapLotIdSuffixNum", 11);
		stubExchange.setProperty("nextSeqNum", 0);
		stubExchange.setProperty("processDate", "0");
		SAPLotIdGenerator lip = new SAPLotIdGenerator();
		lip.process(stubExchange);
		fail("Expected ParseException to be thrown.");
	}
}
