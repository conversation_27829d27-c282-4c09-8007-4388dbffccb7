package com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.test;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.camel.Exchange;
import org.apache.camel.ExchangePattern;
import org.apache.camel.component.mock.MockEndpoint;
import org.apache.camel.model.dataformat.BeanioDataFormat;
import org.apache.camel.test.spring.CamelSpringTestSupport;
import org.junit.Before;
import org.junit.Test;
import org.springframework.context.support.AbstractApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;
import org.springframework.dao.DataAccessException;

import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenRootGroup;

public class RouterTest extends CamelSpringTestSupport {

	BeanioDataFormat eigenDataFormat;

	private static final int ASSERT_PERIOD = 500;

	@Override
	protected AbstractApplicationContext createApplicationContext() {
		return new ClassPathXmlApplicationContext("camel-context-test.xml");
	}

	@Before
	public void init() throws DataAccessException {
		eigenDataFormat = (BeanioDataFormat) this.context.getDataFormats().get("eigenDataFormat");
	}

	

	void assertEqualsResource(String goldFileResourcePath, String testString) {
		String goldString = context
				.getTypeConverter()
				.convertTo(String.class, this.getClass().getResourceAsStream(goldFileResourcePath));
		// cleanse line terminators
		testString = testString.replaceAll("\\r\\n?", "\n");
		goldString = goldString.replaceAll("\\r\\n?", "\n");
		assertEquals(goldString, testString);
	}

	@Test
	public void testRouterFourWaySplit() throws Exception {
		InputStream validFileStream = this.getClass().getResourceAsStream("/data/four_way_split/valid_opac_4_types.txt");
		String fileContent = context.getTypeConverter().convertTo(String.class, validFileStream);
		Exchange exchange = getMandatoryEndpoint("direct:routerTest").createExchange(ExchangePattern.InOut);
		exchange.getIn().setBody(fileContent);
		template.send("direct:routerTest", exchange);

		//Check PS router result
		MockEndpoint resultEndpoint = context.getEndpoint("mock:endRouterTest", MockEndpoint.class);
		resultEndpoint.setAssertPeriod(ASSERT_PERIOD);
		resultEndpoint.expectedMessageCount(1);
		resultEndpoint.assertIsSatisfied();
		Exchange end = resultEndpoint.getExchanges().get(0);

		Map<String, String> prop2file = new HashMap<String, String>() {
			private static final long serialVersionUID = 1L;
		{
			put("EigenRootGroupForSapAmex", "opac_sap_ax.gold");
			put("EigenRootGroupForSapMastercard", "opac_sap_mc.gold");
			put("EigenRootGroupForSapVisa", "opac_sap_vi.gold");
			put("EigenRootGroupForSapDeclined", "opac_sap_declined.gold");
		}};

		for (Map.Entry<String, String> entry: prop2file.entrySet()) {
			String exProp = entry.getKey();
			String fileName = entry.getValue();
			@SuppressWarnings("unchecked")
			List<EigenRootGroup> routedRootGroup = end.getProperty(exProp, List.class);
			ByteArrayOutputStream marshalBuffer = new ByteArrayOutputStream();
			eigenDataFormat.getDataFormat().marshal(exchange, routedRootGroup, marshalBuffer);
			String actualContent = marshalBuffer.toString(StandardCharsets.UTF_8.toString());
			assertEqualsResource("/data/four_way_split/" + fileName, actualContent);
		}
	}

	@Test
	public void testRouterNullBody() throws InterruptedException {
		Exchange exchange = getMandatoryEndpoint("direct:routerTest").createExchange(ExchangePattern.InOut);
		exchange.getIn().setBody("");
		template.send("direct:routerTest", exchange);

		MockEndpoint resultEndpoint = context.getEndpoint("mock:endRouterTest", MockEndpoint.class);
		resultEndpoint.expectedMessageCount(0);
		resultEndpoint.setAssertPeriod(ASSERT_PERIOD);
		resultEndpoint.assertIsSatisfied();

		assertEquals(resultEndpoint.getExchanges().size(),0);
	}
}
