package com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.test;

import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.OPACFileBusinessRulesValidator;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenRootGroup;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.exception.ValidationException;
import org.apache.camel.CamelContext;
import org.apache.camel.Exchange;
import org.apache.camel.ExchangePattern;
import org.apache.camel.component.mock.MockEndpoint;
import org.apache.camel.impl.DefaultCamelContext;
import org.apache.camel.impl.DefaultExchange;
import org.apache.camel.test.spring.CamelSpringTestSupport;
import org.junit.Before;
import org.junit.Test;
import org.springframework.context.support.AbstractApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import java.io.InputStream;
import java.util.ArrayList;


public class OPACBusinessRulesValidatorTest extends CamelSpringTestSupport {
	CamelContext stubContext;
	Exchange stubExchange;

	@Override
	protected AbstractApplicationContext createApplicationContext() {
		return new ClassPathXmlApplicationContext("camel-context-test.xml");
	}

	@Before
	public void init() {
		stubContext = new DefaultCamelContext();
		stubExchange = new DefaultExchange(stubContext);
	}

	@Test
	public void testValid() throws InterruptedException {
		InputStream inputFileStream = this.getClass().getResourceAsStream("/data/valid_opac.txt");
		String fileContent = context.getTypeConverter().convertTo(String.class, inputFileStream);
		Exchange exchange = getMandatoryEndpoint("seda:startOpacValidatorTest").createExchange(ExchangePattern.InOut);
		exchange.getIn().setBody(fileContent);

		Exchange out = template.send("seda:startOpacValidatorTest", exchange);
		assertNull(out.getException());

		MockEndpoint resultEndpoint = context.getEndpoint("mock:validatorOpacTestResult", MockEndpoint.class);
		resultEndpoint.expectedMessageCount(1);
		resultEndpoint.setAssertPeriod(1000);
		resultEndpoint.assertIsSatisfied();

		MockEndpoint dlqEndpoint = context.getEndpoint("mock:deadLetterQueue", MockEndpoint.class);
		dlqEndpoint.expectedMessageCount(0);
		dlqEndpoint.setAssertPeriod(1000);
		dlqEndpoint.assertIsSatisfied();
	}

	@Test
	public void testInvalidInput() throws InterruptedException {
		InputStream inputFileStream = this.getClass().getResourceAsStream("/data/invalid_opac_lineCnt_totalAmt.txt");
		String fileContent = context.getTypeConverter().convertTo(String.class, inputFileStream);
		Exchange exchange = getMandatoryEndpoint("seda:startOpacValidatorTest").createExchange(ExchangePattern.InOut);
		exchange.getIn().setBody(fileContent);

		Exchange out = template.send("seda:startOpacValidatorTest", exchange);
		String expectedMessage = "Invalid paymentType:0 for customerId:000983550000; Credit Card type is invalid: " +
				"(Received Credit Card type=              V), (Expected Credit Card type = VI,MC,AX);  " +
				"TotalBatchAmountPaid from BatchDetails != batch trailer's TotalBatchAmount:122718.00!=122777.00;  " +
				"BatchDetails.size != batch trailer's Total Detail Lines In Batch10!=1; " +
				"totalBatchAmount from batches != file trailer's TotalFileAmount:122777.00!=122778; " +
				"totalDetailLinesInBatch from batches != file trailer's Total Details lines in File1!=2";
		assertEquals(expectedMessage, out.getException().getMessage());

		MockEndpoint resultEndpoint = context.getEndpoint("mock:validatorOpacTestResult", MockEndpoint.class);
		resultEndpoint.expectedMessageCount(0);
		resultEndpoint.setAssertPeriod(1000);
		resultEndpoint.assertIsSatisfied();

		MockEndpoint dlqEndpoint = context.getEndpoint("mock:deadLetterQueue", MockEndpoint.class);
		dlqEndpoint.expectedMessageCount(1);
		dlqEndpoint.setAssertPeriod(1000);
		dlqEndpoint.assertIsSatisfied();
	}

	@Test
	public void testEmptyBody(){
		try {
			Exchange exchange = getMandatoryEndpoint("seda:startOpacValidatorTest").createExchange(ExchangePattern.InOut);
			OPACFileBusinessRulesValidator validator = new OPACFileBusinessRulesValidator();
			exchange.getIn().setBody(new ArrayList<EigenRootGroup>());
			validator.process(stubExchange);
			fail("Expected a ValidationException to be thrown.");
		} catch (ValidationException v) {
			assertEquals("Cannot validate empty file.", v.getMessage());
		}
	}

	@Test
	public void testNullBody() {
		try {
			OPACFileBusinessRulesValidator validator = new OPACFileBusinessRulesValidator();
			stubExchange.getIn().setBody(null);
			validator.process(stubExchange);
			fail("Expected a ValidationException to be thrown.");
		} catch (ValidationException v) {
			assertEquals("Cannot validate empty file.", v.getMessage());
		}
	}

	@Test
	public void testInvalidPaymentType() throws InterruptedException {
		InputStream inputFileStream = this.getClass().getResourceAsStream("/data/invalid_opac_paymentType.txt");
		String fileContent = context.getTypeConverter().convertTo(String.class, inputFileStream);
		Exchange exchange = getMandatoryEndpoint("seda:startOpacValidatorTest").createExchange(ExchangePattern.InOut);
		exchange.getIn().setBody(fileContent);

		Exchange out = template.send("seda:startOpacValidatorTest", exchange);
		String expectedMessage =
				"Invalid paymentType:0 for customerId:   901435281; " +
						"Credit Card type is invalid: (Received Credit Card type=              EA), " +
						"(Expected Credit Card type = VI,MC,AX)";
		assertEquals(expectedMessage, out.getException().getMessage());

		MockEndpoint resultEndpoint = context.getEndpoint("mock:validatorOpacTestResult", MockEndpoint.class);
		resultEndpoint.expectedMessageCount(0);
		resultEndpoint.setAssertPeriod(1000);
		resultEndpoint.assertIsSatisfied();

		MockEndpoint dlqEndpoint = context.getEndpoint("mock:deadLetterQueue", MockEndpoint.class);
		dlqEndpoint.expectedMessageCount(1);
		dlqEndpoint.setAssertPeriod(1000);
		dlqEndpoint.assertIsSatisfied();
	}

	/**
	 * Test validator when Payment type value is less than 2 characters,
	 * valid value: 'VI' invalid value: 'V'
	 * @throws ValidationException
	 */
	@Test
	public void testIncompletePaymentType() throws Exception {
		InputStream inputFileStream = this.getClass().getResourceAsStream("/data/invalid_routed_sap_vi.txt");
		String fileContent = context.getTypeConverter().convertTo(String.class, inputFileStream);
		Exchange exchange = getMandatoryEndpoint("seda:startOpacValidatorTest").createExchange(ExchangePattern.InOut);
		exchange.getIn().setBody(fileContent);

		Exchange out = template.send("seda:startOpacValidatorTest", exchange);
		String expectedMessage = "Invalid paymentType:0 for customerId:   901506157; " +
				"Credit Card type is invalid: (Received Credit Card type=              V), " +
				"(Expected Credit Card type = VI,MC,AX)";
		assertEquals(expectedMessage, out.getException().getMessage());

		MockEndpoint resultEndpoint = context.getEndpoint("mock:validatorOpacTestResult", MockEndpoint.class);
		resultEndpoint.expectedMessageCount(0);
		resultEndpoint.setAssertPeriod(1000);
		resultEndpoint.assertIsSatisfied();

		MockEndpoint dlqEndpoint = context.getEndpoint("mock:deadLetterQueue", MockEndpoint.class);
		dlqEndpoint.expectedMessageCount(1);
		dlqEndpoint.setAssertPeriod(1000);
		dlqEndpoint.assertIsSatisfied();
	}

	@Test
	public void testInvalidEchoData() throws Exception {
		InputStream inputFileStream = this.getClass().getResourceAsStream("/data/invalid_opac_echo_data.txt");
		String fileContent = context.getTypeConverter().convertTo(String.class, inputFileStream);

		Exchange exchange = getMandatoryEndpoint("seda:startOpacValidatorTest").createExchange(ExchangePattern.InOut);
		exchange.setProperty("nextSeqNum", 960);
		exchange.setProperty("unmarshalledPaymentsFile", "");
		exchange.getIn().setBody(fileContent);

		Exchange out = template.send("seda:startOpacValidatorTest", exchange);
		String errorMessage1 = "Line count mismatch: base64EchoData.getTotalRecords (IPAC)=22; batchTrailerTotalLines (OPAC)=25; fileTrailerTotalLines (OPAC)=24; actualFileTotalLines (OPAC)=23";
		String errorMessage2 = "Amount mismatch: base64EchoData.getTotalAmount (IPAC)=50000; batchTrailerTotalAmount (OPAC)=50080; fileTrailerTotalAmount (OPAC)=50079; actualFileTotalAmount (OPAC)=50078;  TotalBatchAmountPaid from BatchDetails != batch trailer's TotalBatchAmount:50078.00!=50080.00;  BatchDetails.size != batch trailer's Total Detail Lines In Batch23!=25; totalBatchAmount from batches != file trailer's TotalFileAmount:50080.00!=50079; totalDetailLinesInBatch from batches != file trailer's Total Details lines in File25!=24";;
		assertStringContains(out.getException().getMessage(), errorMessage1);
		assertStringContains(out.getException().getMessage(), errorMessage2);

		MockEndpoint resultEndpoint = context.getEndpoint("mock:validatorOpacTestResult", MockEndpoint.class);
		resultEndpoint.expectedMessageCount(0);
		resultEndpoint.setAssertPeriod(1000);
		resultEndpoint.assertIsSatisfied();

		MockEndpoint dlqEndpoint = context.getEndpoint("mock:deadLetterQueue", MockEndpoint.class);
		dlqEndpoint.expectedMessageCount(1);
		dlqEndpoint.setAssertPeriod(1000);
		dlqEndpoint.assertIsSatisfied();
	}

	@Test
	public void testInvalidFileSequenceNum() throws Exception {
		InputStream inputFileStream = this.getClass().getResourceAsStream("/data/invalid_opac_echo_data.txt");
		String fileContent = context.getTypeConverter().convertTo(String.class, inputFileStream);

		Exchange exchange = getMandatoryEndpoint("seda:startOpacValidatorTest").createExchange(ExchangePattern.InOut);
			exchange.setProperty("nextSeqNum", 961);
		exchange.setProperty("unmarshalledPaymentsFile", "unmarshalledPaymentsFile");
		exchange.getIn().setBody(fileContent);

		Exchange out = template.send("seda:startOpacValidatorTest", exchange);
		String errorMessage = "Header fileNumber != nextSeqNum (960 != 961)";
		assertStringContains(out.getException().getMessage(), errorMessage);

		MockEndpoint resultEndpoint = context.getEndpoint("mock:validatorOpacTestResult", MockEndpoint.class);
		resultEndpoint.expectedMessageCount(0);
		resultEndpoint.setAssertPeriod(1000);
		resultEndpoint.assertIsSatisfied();

		MockEndpoint dlqEndpoint = context.getEndpoint("mock:deadLetterQueue", MockEndpoint.class);
		dlqEndpoint.expectedMessageCount(1);
		dlqEndpoint.setAssertPeriod(1000);
		dlqEndpoint.assertIsSatisfied();
	}
}
