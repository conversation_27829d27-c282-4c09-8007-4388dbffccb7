package com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.test;

import org.junit.Test;
import org.meanbean.test.BeanTester;

import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.aws.PaymentHubMetaDataResponse;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenBatch;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenBatchDetail;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenBatchHeader;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenBatchTrailer;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenFileHeader;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenFileTrailer;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenRootGroup;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.sap.BFKKZGR00;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.sap.BFKKZK;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.sap.BFKKZP;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.sap.SAPRootGroup;


public class BeanTest {

	@Test
	public void testDataModel() {
		new BeanTester().testBean(EigenBatch.class);
		new BeanTester().testBean(EigenBatchDetail.class);
		new BeanTester().testBean(EigenBatchHeader.class);
		new BeanTester().testBean(EigenBatchTrailer.class);
		new BeanTester().testBean(EigenFileHeader.class);
		new BeanTester().testBean(EigenFileTrailer.class);
		new BeanTester().testBean(EigenRootGroup.class);

		new BeanTester().testBean(BFKKZGR00.class);
		new BeanTester().testBean(BFKKZK.class);
		new BeanTester().testBean(BFKKZP.class);
		new BeanTester().testBean(SAPRootGroup.class);

		new BeanTester().testBean(PaymentHubMetaDataResponse.class);
	}
}
