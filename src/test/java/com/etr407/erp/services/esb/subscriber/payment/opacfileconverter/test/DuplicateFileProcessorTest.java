package com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.test;

import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.DuplicateFileProcessor;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenRootGroup;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.exception.DuplicateFileException;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.exception.ValidationException;
import org.apache.camel.Exchange;
import org.apache.camel.impl.DefaultExchange;
import org.apache.camel.model.dataformat.BeanioDataFormat;
import org.apache.camel.test.spring.CamelSpringTestSupport;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.TemporaryFolder;
import org.springframework.context.support.AbstractXmlApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import java.io.InputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;


public class DuplicateFileProcessorTest extends CamelSpringTestSupport {

	Exchange stubExchange;
	DuplicateFileProcessor duplicateFileProcessor;

	@Rule
	public TemporaryFolder tempFolder = new TemporaryFolder();

	@Override
	protected AbstractXmlApplicationContext createApplicationContext() {
		ClassPathXmlApplicationContext classPathXmlApplicationContext = new ClassPathXmlApplicationContext("camel-context-test.xml");
		return classPathXmlApplicationContext;
	}

	@Before
	public void init() {
		stubExchange = new DefaultExchange(super.context);
		duplicateFileProcessor = new DuplicateFileProcessor(tempFolder.getRoot().getAbsolutePath(),60);
	}

	/**
	 * Loads a single file and contents into the stub exchange object.
	 */
	private void prepareExchange() throws Exception {
		String origFileName = "opac_request_20190807.txt";

		// load and unmarshal file
		BeanioDataFormat format = (BeanioDataFormat) stubExchange.getContext().getDataFormats().get("eigenDataFormat");
		InputStream data = this.getClass().getResourceAsStream("/data/" + origFileName);
		List<EigenRootGroup> body = (ArrayList<EigenRootGroup>) format.getDataFormat().unmarshal(stubExchange, data);

		// setup exchange
		stubExchange.setProperty("origFileName", origFileName);
		stubExchange.getIn().setBody(body);
	}

	/**
	 * Test to check if a file with duplicate name is caught
	 * and DuplicateFileException is thrown
	 * @throws DuplicateFileException
	 */
	@Test
	public void testSingleMatch() throws Exception {
		try {
			// setup temp folder
			String fileNameWithSeqNum = "opac_request_20190807.txt";
			tempFolder.newFile("opac_request_20190807.txt-20190807042354657");

			prepareExchange();

			// process
			duplicateFileProcessor.process(stubExchange);
			fail("Expected DuplicateFileException to be thrown.");
		} catch (DuplicateFileException d) {
			String expectedMsg = "Duplicate files found: opac_request_20190807.txt";
			assertEquals(expectedMsg, d.getMessage());
		}
	}

	/**
	 * Test to check against empty archive directory.
	 * @throws DuplicateFileException
	 */
	@Test
	public void testNoMatchEmptyArchive() throws Exception {
		prepareExchange();
		duplicateFileProcessor.process(stubExchange);
	}

	/**
	 * Test that the input file is unique from the archived files.
	 * @throws Exception
	 */
	@Test
	public void testNoMatchPopulatedArchive() throws Exception {
		prepareExchange();

		// create files for the last 60 days in the temp folder
		DateFormat df = new SimpleDateFormat("yyyyMMdd");
		DateFormat tf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
		Calendar cal = new GregorianCalendar();
		for (int i = 1; i <= 70; i++) {
			cal.add(Calendar.DAY_OF_MONTH, -1);
			Date previousDate = cal.getTime();

			String fileName = String.format(
					"opac_request_%s.txt-%s",
					df.format(previousDate),
					tf.format(previousDate)
			);
			tempFolder.newFile(String.format(fileName)).setLastModified(previousDate.getTime());
		}

		duplicateFileProcessor.process(stubExchange);
	}

	/**
	 * Test when the archive directory is missing.
	 */
	@Test
	public void testMissingArchiveDirectory() throws Exception {
		try {
			prepareExchange();
			tempFolder.delete();
			duplicateFileProcessor.process(stubExchange);
			fail("Expected ValidationException to be thrown.");
		} catch (ValidationException e) {
			String expectedMsg = String.format(
					"Done directory location doesn't exist, location=%s",
					tempFolder.getRoot().getAbsolutePath());
			 assertEquals(expectedMsg, e.getMessage());
		}
	}
}
