package com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.test;

import org.apache.camel.CamelContext;
import org.apache.camel.Exchange;
import org.apache.camel.impl.DefaultCamelContext;
import org.apache.camel.impl.DefaultExchange;
import org.apache.camel.test.spring.CamelSpringTestSupport;
import org.junit.Before;
import org.junit.Test;
import org.springframework.context.support.AbstractApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.OPACFailureProcessor;

public class OPACFailureProcessorTest extends CamelSpringTestSupport {
	OPACFailureProcessor eigenFailureProcessor;
	CamelContext stubContext;
	Exchange stubExchange;

	@Override
	protected AbstractApplicationContext createApplicationContext() {
		return new ClassPathXmlApplicationContext("camel-context-test.xml");
	}

	@Before
	public void init() {
		stubExchange = new DefaultExchange(new DefaultCamelContext());
		stubContext = new DefaultCamelContext();
		eigenFailureProcessor = new OPACFailureProcessor();
	}
	/**
	 * Test that the input file has valid exchange properties
	 * set in its context
	 * @throws Exception
	 */
	@Test
	public void testValidInputs() throws Exception {

		stubExchange.setProperty("origFileName", "input.eigen.txt");
		stubExchange.setProperty("fileNameWithoutExt", "input.eigen");

		eigenFailureProcessor.process(stubExchange);
		assertEquals(stubExchange.getIn().getHeader("errorFileHeader"), "input.eigen");
		assertEquals(stubExchange.getIn().getHeader("fileExt"), "txt");
	}
	/**
	 * Test when exchange properties are not set
	 *
	 * @throws Exception
	 */
	@Test
	public void testInvalidInputs() {
		try {
			eigenFailureProcessor.process(stubExchange);
		} catch (Exception exception) {
			assertEquals(stubExchange.getIn().getHeader("errorFileHeader"), null);
			assertEquals(stubExchange.getIn().getHeader("fileExt"), null);
			// TODO: test exception as well
		}
	}
	/**
	 * Test when exchange properties are set to Blank values
	 * @throws Exception
	 */
	@Test
	public void testBlankExchangePropertyInputs() {
		try {
			stubExchange.getIn().setHeader("origFileName", " ");
			stubExchange.getIn().setHeader("fileNameWithoutExt", " ");
			eigenFailureProcessor.process(stubExchange);
		} catch (Exception exception) {
			assertEquals(stubExchange.getIn().getHeader("errorFileHeader"), null);
			assertEquals(stubExchange.getIn().getHeader("fileExt"), null);
			// TODO: test exception as well
		}
	}
	/**
	 * Test when input file name contains multiple dots
	 *
	 * @throws Exception
	 */
	@Test
	public void testValidLongInputFileName() throws Exception {

		stubExchange.setProperty("origFileName", "input_eigen_declined.210115.0110.txt");
		stubExchange.setProperty("fileNameWithoutExt", "input_eigen_declined.210115.0110");

		eigenFailureProcessor.process(stubExchange);
		assertEquals(stubExchange.getIn().getHeader("errorFileHeader"), "input_eigen_declined.210115.0110");
		assertEquals(stubExchange.getIn().getHeader("fileExt"), "txt");
	}

}
