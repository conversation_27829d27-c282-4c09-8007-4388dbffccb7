package com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.test;

import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.SapFileNameAnalyzer;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.exception.ValidationException;
import org.apache.camel.CamelContext;
import org.apache.camel.Exchange;
import org.apache.camel.impl.DefaultCamelContext;
import org.apache.camel.impl.DefaultExchange;
import org.apache.camel.test.spring.CamelSpringTestSupport;
import org.junit.Before;
import org.junit.Test;
import org.springframework.context.support.AbstractXmlApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

public class SapFileNameAnalyzerTest extends CamelSpringTestSupport {
	CamelContext stubContext;
	Exchange stubExchange;

	@Override
	protected AbstractXmlApplicationContext createApplicationContext() {
		return new ClassPathXmlApplicationContext("camel-context-test.xml");
	}

	@Before
	public void init() {
		stubContext = new DefaultCamelContext();
		stubExchange = new DefaultExchange(stubContext);
	}

	@Test
	public void testValidFileName() throws Exception {
		Exchange exchange = new DefaultExchange(context);

		exchange.getIn().setHeader("CamelFileName", "A_SAP_17_PID_CID");
		SapFileNameAnalyzer sfa = new SapFileNameAnalyzer();
		sfa.process(exchange);
		assertEquals(17, exchange.getProperty("nextSeqNum"));
		assertEquals("PID_CID", exchange.getProperty("trackingId"));
	}

	@Test
	public void testInvalidFileName() {
		try {
			Exchange exchange = new DefaultExchange(context);
			exchange.getIn().setHeader("CamelFileName", "A_SAP_17");
			SapFileNameAnalyzer sfa = new SapFileNameAnalyzer();
			sfa.process(exchange);
			fail("Expected ValidationException to be thrown.");
		} catch (ValidationException e) {
			assertEquals("File name format not correct", e.getMessage());
		}
	}

	@Test
	public void testEmptyFileName() {
		try {
			Exchange exchange = new DefaultExchange(context);
			exchange.getIn().setHeader("CamelFileName", "");
			SapFileNameAnalyzer sfa = new SapFileNameAnalyzer();
			sfa.process(exchange);
			fail("Expected ValidationException to be thrown.");
		} catch (ValidationException e) {
			assertEquals("File name format not correct", e.getMessage());
		}
	}

	@Test
	public void testNullFileName() {
		try {
			Exchange exchange = new DefaultExchange(context);
			exchange.getIn().setHeader("CamelFileName", null);
			SapFileNameAnalyzer sfa = new SapFileNameAnalyzer();
			sfa.process(exchange);
			fail("Expected ValidationException to be thrown.");
		} catch (ValidationException e) {
			assertEquals("No file name specified", e.getMessage());
		}
	}

}
