package com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.test;

import static org.junit.Assert.assertEquals;

import org.junit.Test;

import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.utils.Base64EchoData;

public class Base64EchoDataTest {

	@Test
	public void testValidBase64String() {
		long totalRecords = 99999999L;
		long totalAmount = 8888888888888888L;
		
		Base64EchoData b64FromValues = new Base64EchoData(totalRecords, totalAmount);
		Base64EchoData b64FromString = new Base64EchoData(b64FromValues.toString());
		assertEquals(b64FromValues, b64FromString);
	}


	@Test
	public void testZeroed() {
		long totalRecords = 0L;
		long totalAmount = 0L;
		
		Base64EchoData b64FromValues = new Base64EchoData(totalRecords, totalAmount);
		Base64EchoData b64FromString = new Base64EchoData(b64FromValues.toString());
		assertEquals(b64FromValues, b64FromString);
	}

	@Test(expected = IllegalArgumentException.class)
	public void testInvalidBase64String() {
		new Base64EchoData("c21hbGw=");
	}
}
