service.getToken.url=https://esb-qat.407etr.com/apis/integration/oauth2/v1/token/

proxy.file.uri=https4://services-qat.407etr.com:8443/proxy/erp/ph/v1/eigenOpac/
proxy.fileList.uri=https4://services-qat.407etr.com:8443/proxy/erp/ph/v1/eigenOpac/fileList

oauth.esbClientId=<EMAIL>

sap.client=200

spring.datasource.url=*************************************
spring.datasource.username=RCOLPSFR

zookeeper.cluster=wdbq-int-cls01,mkhq-int-cls01,mkhq-int-cls02:2181

aws.paymentHubMetaData.endpoint=7zjkn62529-vpce-012e35b42aad861c7.execute-api.ca-central-1.amazonaws.com/qa/paymenthub/metadata/
aws.connectTimeout=30000
aws.requestTimeout=30000
aws.client.id=<EMAIL>

validation.feature.flag=true
