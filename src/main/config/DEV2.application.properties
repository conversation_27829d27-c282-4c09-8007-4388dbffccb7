service.getToken.url=http://esb-dev2.407etr.com/apis/integration/oauth2/v1/token/

proxy.file.uri=https4://services-dev2.407etr.com:8443/proxy/erp/ph/v1/eigenOpac/
proxy.fileList.uri=https4://services-dev2.407etr.com:8443/proxy/erp/ph/v1/eigenOpac/fileList

oauth.esbClientId=<EMAIL>.dev2

sap.client=210

spring.datasource.url=******************************
spring.datasource.username=RCOLPSFR

zookeeper.cluster=WDBD-INT-CLS11,WDBD-INT-CLS12,MKHD-INT-CLS11:2181

aws.paymentHubMetaData.endpoint=mnzv25nqwf-vpce-012e35b42aad861c7.execute-api.ca-central-1.amazonaws.com/dev2/paymenthub/metadata/
aws.connectTimeout=30000
aws.requestTimeout=30000
aws.client.id=<EMAIL>.dev2

validation.feature.flag=true
