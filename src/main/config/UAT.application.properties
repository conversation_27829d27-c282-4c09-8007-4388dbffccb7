service.getToken.url=https://esb-uat.407etr.com/apis/integration/oauth2/v1/token/

proxy.file.uri=https4://services-uat.407etr.com:8443/proxy/erp/ph/v1/eigenOpac/
proxy.fileList.uri=https4://services-uat.407etr.com:8443/proxy/erp/ph/v1/eigenOpac/fileList

oauth.esbClientId=<EMAIL>

sap.client=200

spring.datasource.url=*************************************
spring.datasource.username=RCOLPSFR

zookeeper.cluster=wdbu-int-cls01,mkhu-int-cls01,mkhu-int-cls02:2181

aws.paymentHubMetaData.endpoint=dvws35plq5-vpce-0ea17c96f7d094f5a.execute-api.ca-central-1.amazonaws.com/uat/paymenthub/metadata/
aws.connectTimeout=30000
aws.requestTimeout=30000
aws.client.id=<EMAIL>

validation.feature.flag=false
