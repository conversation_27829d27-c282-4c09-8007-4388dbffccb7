service.getToken.url=https://esb.407etr.com/apis/integration/oauth2/v1/token/

proxy.file.uri=https4://services.407etr.com:8443/proxy/erp/ph/v1/eigenOpac/
proxy.fileList.uri=https4://services.407etr.com:8443/proxy/erp/ph/v1/eigenOpac/fileList

oauth.esbClientId=<EMAIL>

sap.client=200

spring.datasource.url=*************************************
spring.datasource.username=RCOLPSFR

zookeeper.cluster=wdbp-int-cls01,mkhp-int-cls01,mkhp-int-cls02:2181

aws.paymentHubMetaData.endpoint=7cw0vgwrce-vpce-0cec064de0c1c792b.execute-api.ca-central-1.amazonaws.com/prod/paymenthub/metadata/
aws.connectTimeout=30000
aws.requestTimeout=30000
aws.client.id=<EMAIL>
validation.feature.flag=false
