service.getToken.url=https://esb-qat2.407etr.com/apis/integration/oauth2/v1/token/

proxy.file.uri=https4://services-qat2.407etr.com:8443/proxy/erp/ph/v1/eigenOpac/
proxy.fileList.uri=https4://services-qat2.407etr.com:8443/proxy/erp/ph/v1/eigenOpac/fileList

oauth.esbClientId=<EMAIL>.qa2

sap.client=200

spring.datasource.url=******************************
spring.datasource.username=RCOLPSFR

zookeeper.cluster=WDBQ-INT-CLS11,WDBQ-INT-CLS12,MKHQ-INT-CLS11:2181

aws.paymentHubMetaData.endpoint=zbwmh2u6sg-vpce-012e35b42aad861c7.execute-api.ca-central-1.amazonaws.com/qa2/paymenthub/metadata/
aws.connectTimeout=30000
aws.requestTimeout=30000
aws.client.id=<EMAIL>.qa2

validation.feature.flag=true
