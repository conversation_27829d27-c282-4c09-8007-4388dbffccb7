#!/usr/bin/env python2

import json
import logging
import sys
from argparse import ArgumentParser
from urllib import urlencode
from urllib2 import HTTPError, Request, urlopen


logging.basicConfig(
    format='%(levelname)s - %(module)s - %(message)s',
    level=logging.INFO)


AUTH_ENDPOINT = '${auth.url}'
AUTH_DATA = {
    'grant_type': 'client_credentials',
    'client_id': '${auth.clientId}',
    'client_secret': '{{ lookup('passwordstate', 'oauth:paymenthub_oauth_esbclient', field='title', listName='app') }}',
    'scope': 'paymenthub:request'
}
TARGET_TO_URL_MAP = {
    'start': '${invoke.url}',
    'router': '${router.url}'
}
POST_HEADERS = {
    'Content-Type' : 'application/x-www-form-urlencoded',
    'Accept': 'application/json;charset=utf-8'
}


def get_token():
    logging.info('requesting credentials from %s', AUTH_ENDPOINT)

    auth_request = Request(
        AUTH_ENDPOINT,
        headers=POST_HEADERS,
        data=urlencode(AUTH_DATA))

    try:
        response = urlopen(auth_request)
        content = response.read().encode('UTF-8')
        token_data = json.loads(content)
        token = "Bearer " + token_data['access_token']
        logging.info("successfully acquired credentials")
        return token
    except HTTPError as e:
        logging.exception('failed to acquire credentials')


def invoke(token, url):
    logging.info('invoking Payment Hub integration at %s', url)

    headers = {'Authorization': token}
    headers.update(POST_HEADERS)
    invoke_request = Request(url, headers=headers, data="")

    try:
        response = urlopen(invoke_request)
        content = response.read().encode('UTF-8')
        logging.info('integration invoked successfully: %s', content)
        return True
    except HTTPError as e:
        logging.exception('failed to invoke Payment Hub integration')


if __name__ == '__main__':
    parser = ArgumentParser(
        description='Execute or resume the Payment Hub ' \
                    'OPAC Payment file transmission process.')
    parser.add_argument('target', help='target invocation point',
                        nargs='?', choices=['start', 'router'],
                        default='start')
    parser.epilog = 'Each target is associated with a specific stage in the ' \
                    'process. The "start" target, which corresponds to the ' \
                    'initial stage, will execute by default if no arguments ' \
                    'are provided.'

    args = parser.parse_args()
    target_url = TARGET_TO_URL_MAP[args.target]
    logging.info('target invocation point: %s -> %s', args.target, target_url)
    
    token = get_token()
    if token is None:
        logging.critical('could not acquire an authorization token')
        sys.exit(1)
    result = invoke(token, target_url)
    if not result:
        logging.critical('failed to start Payment Hub integration')
        sys.exit(1)
