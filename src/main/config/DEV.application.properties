service.getToken.url=http://esb-dev.407etr.com/apis/integration/oauth2/v1/token/

proxy.file.uri=https4://services-dev.407etr.com:8443/proxy/erp/ph/v1/eigenOpac/
proxy.fileList.uri=https4://services-dev.407etr.com:8443/proxy/erp/ph/v1/eigenOpac/fileList

oauth.esbClientId=<EMAIL>

sap.client=210

spring.datasource.url=******************************
spring.datasource.username=RCOLPSFR

zookeeper.cluster=wdbd-int-cls01,mkhd-int-cls01,mkhd-int-cls02:2181

aws.paymentHubMetaData.endpoint=3o7k8vk752-vpce-012e35b42aad861c7.execute-api.ca-central-1.amazonaws.com/dev/paymenthub/metadata/
aws.connectTimeout=30000
aws.requestTimeout=30000
aws.client.id=<EMAIL>

validation.feature.flag=true
