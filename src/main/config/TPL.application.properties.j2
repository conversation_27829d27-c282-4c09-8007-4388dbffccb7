
router.inbox=/407/apps/transfers/PH/OPAC/ESB/in/
router.error=/407/apps/transfers/PH/OPAC/ESB/in/error/
router.done=/407/apps/PaymentHub/Archive/OPAC/ESB/in/done/

sapMapper.inbox=/407/apps/transfers/PH/OPAC/ESB/in/routed/SAP/
sapMapper.error=/407/apps/transfers/PH/OPAC/ESB/in/routed/SAP/error/
sapMapper.done=/407/apps/PaymentHub/Archive/OPAC/ESB/in/routed/SAP/done/
sapMapper.outbox=/407/apps/efs/PH/OPAC/SAP/approved_out/
sapMapper.declinedOutbox=/407/apps/efs/PH/OPAC/SAP/declined_out/

cashAdmin.dropbox=/407/apps/PaymentHub/CashAdmin/Daily/PAC/
cashAdmin.dropbox.permissions=660

duplicate.fileCheck.age=60

sapMapper.outbox.permissions=660

sap.client=${sap.client}

service.getToken.url=${service.getToken.url}

proxy.file.uri=${proxy.file.uri}
proxy.fileList.uri=${proxy.fileList.uri}

oauth.esbClientId=${oauth.esbClientId}
oauth.esbClientSecret={{ lookup('passwordstate', 'oauth:paymenthub_oauth_esbclient', field='title', listName='app') }}
oauth.esbScope=paymenthub:request
oauth.requiredScope=paymenthub:request


aws.paymentHubMetaData.endpoint=${aws.paymentHubMetaData.endpoint}
aws.connectTimeout=${aws.connectTimeout}
aws.requestTimeout=${aws.requestTimeout}

aws.client.id=${aws.client.id}
aws.client.secret={{ lookup('passwordstate', 'oauth:paymenthub_oauth_esbclient', field='title', listName='app') }}
aws.client.scope=paymenthub:request

sap.client=${sap.client}

db.batchquerysize = 1000

zookeeper.cluster=${zookeeper.cluster}
zookeeper.zNode=paymentHub/opacFileConverter

log.errorTag=Splunk-[Payment Hub]-[Support - App. Support Customer]-[P2-C1-H12]

fileconverter.messageTracking.information=fileName

validation.feature.flag=${validation.feature.flag}
