package org.example.batchdemo.controller;

import org.example.batchdemo.service.FileProcessingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/opac")
public class FileProcessingController {

    private static final Logger logger = LoggerFactory.getLogger(FileProcessingController.class);

    @Autowired
    private FileProcessingService fileProcessingService;

    @PostMapping("/fetch")
    public ResponseEntity<Map<String, String>> fetchFiles() {
        try {
            logger.info("Received request to fetch files");
            String result = fileProcessingService.startFileFetch();
            
            Map<String, String> response = new HashMap<>();
            response.put("message", result);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error in fetch endpoint", e);
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", "Internal server error");
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    @PostMapping("/router")
    public ResponseEntity<Map<String, String>> startProcessing() {
        try {
            logger.info("Received request to start file processing");
            String result = fileProcessingService.startFileProcessing();
            
            Map<String, String> response = new HashMap<>();
            response.put("message", result);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error in router endpoint", e);
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", "Internal server error");
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    @PostMapping("/validate")
    public ResponseEntity<Map<String, String>> validateFile() {
        try {
            logger.info("Received request to validate file");
            String result = fileProcessingService.validateFile();
            
            Map<String, String> response = new HashMap<>();
            response.put("message", result);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error in validate endpoint", e);
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", "Internal server error");
            return ResponseEntity.status(500).body(errorResponse);
        }
    }
}
