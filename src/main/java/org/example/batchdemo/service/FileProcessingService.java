package org.example.batchdemo.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FileProcessingService {

    private static final Logger logger = LoggerFactory.getLogger(FileProcessingService.class);

    @Autowired
    private JobLauncher jobLauncher;

    @Autowired
    private Job opacFileProcessingJob;

    public String startFileProcessing() {
        try {
            logger.info("Starting OPAC file processing job");
            
            JobParameters jobParameters = new JobParametersBuilder()
                    .addLong("startTime", System.currentTimeMillis())
                    .toJobParameters();

            jobLauncher.run(opacFileProcessingJob, jobParameters);
            
            logger.info("OPAC file processing job started successfully");
            return "File processing initiated successfully";
            
        } catch (Exception e) {
            logger.error("Error starting file processing job", e);
            throw new RuntimeException("Failed to start file processing: " + e.getMessage(), e);
        }
    }

    public String startFileFetch() {
        logger.info("File fetch initiated");
        // In the original Camel implementation, this would trigger file polling
        // For now, we'll just return a success message
        return "File fetching initiated";
    }

    public String validateFile() {
        logger.info("File validation initiated");
        // This could trigger a validation-only job
        return "File validation initiated";
    }
}
