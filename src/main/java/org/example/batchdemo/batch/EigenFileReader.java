package org.example.batchdemo.batch;

import org.beanio.BeanReader;
import org.beanio.StreamFactory;
import org.example.batchdemo.model.eigen.EigenRootGroup;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.NonTransientResourceException;
import org.springframework.batch.item.ParseException;
import org.springframework.batch.item.UnexpectedInputException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

@Component
public class EigenFileReader implements ItemReader<EigenRootGroup> {

    private static final Logger logger = LoggerFactory.getLogger(EigenFileReader.class);

    @Value("${batch.input.directory:#{systemProperties['java.io.tmpdir']}/batch/input}")
    private String inputDirectory;

    private StreamFactory streamFactory;
    private List<Path> filesToProcess;
    private int currentFileIndex = 0;

    public EigenFileReader() {
        this.streamFactory = StreamFactory.newInstance();
        // Load the BeanIO mapping configuration
        this.streamFactory.loadResource("beanio-mappings.eigen.xml");
        this.filesToProcess = new ArrayList<>();
    }

    @Override
    public EigenRootGroup read() throws Exception, UnexpectedInputException, ParseException, NonTransientResourceException {
        if (filesToProcess.isEmpty()) {
            loadFilesToProcess();
        }

        if (currentFileIndex >= filesToProcess.size()) {
            return null; // No more files to process
        }

        Path currentFile = filesToProcess.get(currentFileIndex);
        currentFileIndex++;

        logger.info("Processing file: {}", currentFile.getFileName());

        try (BufferedReader fileReader = Files.newBufferedReader(currentFile)) {
            BeanReader beanReader = streamFactory.createReader("eigenData", fileReader);
            try {
                EigenRootGroup rootGroup = (EigenRootGroup) beanReader.read();
                if (rootGroup != null) {
                    logger.info("Successfully read file: {}", currentFile.getFileName());
                    return rootGroup;
                } else {
                    logger.warn("No data found in file: {}", currentFile.getFileName());
                    return read(); // Try next file
                }
            } finally {
                if (beanReader != null) {
                    beanReader.close();
                }
            }
        } catch (IOException e) {
            logger.error("Error reading file: {}", currentFile.getFileName(), e);
            throw new ParseException("Failed to read file: " + currentFile.getFileName(), e);
        }
    }

    private void loadFilesToProcess() throws IOException {
        Path inputPath = Paths.get(inputDirectory);
        if (!Files.exists(inputPath)) {
            Files.createDirectories(inputPath);
            logger.info("Created input directory: {}", inputPath);
            return;
        }

        try (DirectoryStream<Path> stream = Files.newDirectoryStream(inputPath, "*.txt")) {
            for (Path file : stream) {
                if (Files.isRegularFile(file)) {
                    filesToProcess.add(file);
                    logger.info("Found file to process: {}", file.getFileName());
                }
            }
        }

        logger.info("Found {} files to process", filesToProcess.size());
    }

    public void reset() {
        currentFileIndex = 0;
        filesToProcess.clear();
    }
}
