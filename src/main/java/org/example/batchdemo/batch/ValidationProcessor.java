package org.example.batchdemo.batch;

import org.example.batchdemo.exception.ValidationException;
import org.example.batchdemo.model.eigen.EigenBatch;
import org.example.batchdemo.model.eigen.EigenBatchDetail;
import org.example.batchdemo.model.eigen.EigenBatchTrailer;
import org.example.batchdemo.model.eigen.EigenFileTrailer;
import org.example.batchdemo.model.eigen.EigenRootGroup;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

@Component
public class ValidationProcessor implements ItemProcessor<EigenRootGroup, EigenRootGroup> {

    private static final Logger logger = LoggerFactory.getLogger(ValidationProcessor.class);

    @Override
    public EigenRootGroup process(EigenRootGroup item) throws Exception {
        logger.info("OPAC File Business Rules Validation Started");
        
        if (item == null) {
            throw new ValidationException("Cannot validate empty file.");
        }

        validateFileStructure(item);
        validateBatches(item.getBatches());
        validateFileTrailer(item);

        logger.info("OPAC File Business Rules Validation Completed Successfully");
        return item;
    }

    private void validateFileStructure(EigenRootGroup rootGroup) throws ValidationException {
        if (rootGroup.getFileHeader() == null) {
            throw new ValidationException("File header is missing");
        }

        if (rootGroup.getBatches() == null || rootGroup.getBatches().isEmpty()) {
            throw new ValidationException("File must contain at least one batch");
        }

        if (rootGroup.getFileTrailer() == null) {
            throw new ValidationException("File trailer is missing");
        }
    }

    private void validateBatches(List<EigenBatch> batches) throws ValidationException {
        for (EigenBatch batch : batches) {
            validateBatch(batch);
        }
    }

    private void validateBatch(EigenBatch batch) throws ValidationException {
        if (batch.getBatchHeader() == null) {
            throw new ValidationException("Batch header is missing");
        }

        if (batch.getBatchDetails() == null || batch.getBatchDetails().isEmpty()) {
            throw new ValidationException("Batch must contain at least one detail record");
        }

        if (batch.getBatchTrailer() == null) {
            throw new ValidationException("Batch trailer is missing");
        }

        validateBatchTrailer(batch);
    }

    private void validateBatchTrailer(EigenBatch batch) throws ValidationException {
        EigenBatchTrailer trailer = batch.getBatchTrailer();
        List<EigenBatchDetail> details = batch.getBatchDetails();

        // Validate detail count
        long expectedDetailCount = details.size();
        if (trailer.getTotalDetailLinesInBatch() != expectedDetailCount) {
            throw new ValidationException(
                String.format("Batch trailer detail count mismatch. Expected: %d, Found: %d",
                    expectedDetailCount, trailer.getTotalDetailLinesInBatch()));
        }

        // Validate batch amount
        BigDecimal calculatedAmount = details.stream()
            .map(EigenBatchDetail::getAmount)
            .filter(amount -> amount != null)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (trailer.getTotalBatchAmount().compareTo(calculatedAmount) != 0) {
            throw new ValidationException(
                String.format("Batch trailer amount mismatch. Expected: %s, Found: %s",
                    calculatedAmount, trailer.getTotalBatchAmount()));
        }
    }

    private void validateFileTrailer(EigenRootGroup rootGroup) throws ValidationException {
        EigenFileTrailer trailer = rootGroup.getFileTrailer();
        List<EigenBatch> batches = rootGroup.getBatches();

        // Validate batch count
        long expectedBatchCount = batches.size();
        if (trailer.getNumberOfBatches() != expectedBatchCount) {
            throw new ValidationException(
                String.format("File trailer batch count mismatch. Expected: %d, Found: %d",
                    expectedBatchCount, trailer.getNumberOfBatches()));
        }

        // Validate total detail lines
        long totalDetailLines = batches.stream()
            .mapToLong(batch -> batch.getBatchDetails().size())
            .sum();

        if (trailer.getTotalDetailLinesInFile() != totalDetailLines) {
            throw new ValidationException(
                String.format("File trailer detail count mismatch. Expected: %d, Found: %d",
                    totalDetailLines, trailer.getTotalDetailLinesInFile()));
        }

        // Validate total file amount
        BigDecimal totalFileAmount = batches.stream()
            .flatMap(batch -> batch.getBatchDetails().stream())
            .map(EigenBatchDetail::getAmount)
            .filter(amount -> amount != null)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (trailer.getTotalFileAmount().compareTo(totalFileAmount) != 0) {
            throw new ValidationException(
                String.format("File trailer amount mismatch. Expected: %s, Found: %s",
                    totalFileAmount, trailer.getTotalFileAmount()));
        }
    }
}
