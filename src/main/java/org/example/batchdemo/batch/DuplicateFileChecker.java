package org.example.batchdemo.batch;

import org.example.batchdemo.exception.DuplicateFileException;
import org.example.batchdemo.exception.ValidationException;
import org.example.batchdemo.model.eigen.EigenRootGroup;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Checks for duplicate files based on input fileName. Throws
 * a DuplicateFileException when a duplicate file is found.
 */
@Component
public class DuplicateFileChecker {

    private static final Logger logger = LoggerFactory.getLogger(DuplicateFileChecker.class);

    private static final String DUPLICATE_FILE_MSG = "Duplicate file found: input file name '%s' matches archived file name '%s'";
    private static final String INVALID_DONE_DIR_MSG = "Invalid done directory: %s";

    @Value("${batch.done.directory:#{systemProperties['java.io.tmpdir']}/batch/done}")
    private String doneDir;

    @Value("${batch.duplicate.fileCheck.age:10}")
    private int fileAge;

    /**
     * Expected vendor filename: opac_request_yyyyMMdd.txt
     * Expected archive filename: opac_request_yyyyMMdd.txt-yyyyMMddHHmmssSSS
     * Sequence number field: EigenFileHeader.efSeqNumber
     * Sets fileNameWithSeqNum=opac_request_yyyyMMdd.nnn.txt
     */
    public void checkForDuplicates(String origFileName, EigenRootGroup eigenRootGroup) 
            throws ValidationException, DuplicateFileException {
        
        // ensure archive directory exists
        if (!new File(this.doneDir).exists()) {
            String formattedMessage = String.format(INVALID_DONE_DIR_MSG, this.doneDir);
            throw new ValidationException(formattedMessage);
        }

        long fileSeqNum = eigenRootGroup.getFileHeader().getEfSeqNumber() % 1000;
        String fileNameWithSeqNum = String.format("%s.%03d.txt", getFileNameWithoutExt(origFileName), fileSeqNum);

        // check for duplicate file names
        logger.info("Checking for duplicates against: fileName={}; fileSeqNum={}", origFileName, fileSeqNum);
        fileNameCheck(origFileName, getSortedFilesPathList());
    }

    private void fileNameCheck(String inputFileName, List<Path> pathList) throws DuplicateFileException {
        logger.info("DuplicateFileChecker.fileNameCheck method start");

        for (int i = 0; i <= fileAge && i < pathList.size(); i++) {
            Path path = pathList.get(i);
            String archivedFileName = path.getFileName().toString();
            String archivedFormattedFileName = getArchiveFileName(archivedFileName);

            logger.info("Found file: fileName={}", archivedFormattedFileName);
            if (inputFileName.equals(archivedFormattedFileName)) {
                String formattedMessage = String.format(
                        DUPLICATE_FILE_MSG, inputFileName, archivedFormattedFileName);
                throw new DuplicateFileException(formattedMessage);
            }
        }

        logger.info("DuplicateFileChecker.fileNameCheck method end");
    }

    private List<Path> getSortedFilesPathList() throws ValidationException {
        List<Path> pathList = new ArrayList<>();
        Path doneDirectory = Paths.get(doneDir);

        try (DirectoryStream<Path> stream = Files.newDirectoryStream(doneDirectory)) {
            for (Path file : stream) {
                if (Files.isRegularFile(file)) {
                    pathList.add(file);
                }
            }
        } catch (IOException e) {
            throw new ValidationException("Error reading done directory: " + doneDir, e);
        }

        // Sort by last modified time (newest first)
        pathList.sort((p1, p2) -> {
            try {
                return Files.getLastModifiedTime(p2).compareTo(Files.getLastModifiedTime(p1));
            } catch (IOException e) {
                logger.warn("Error comparing file times", e);
                return 0;
            }
        });

        return pathList;
    }

    private String getFileNameWithoutExt(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return fileName;
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName;
    }

    private String getArchiveFileName(String archivedFileName) {
        if (archivedFileName == null || archivedFileName.isEmpty()) {
            return archivedFileName;
        }
        
        // Remove timestamp suffix from archived filename
        // Expected format: originalname.txt-yyyyMMddHHmmssSSS
        int lastDashIndex = archivedFileName.lastIndexOf('-');
        return lastDashIndex > 0 ? archivedFileName.substring(0, lastDashIndex) : archivedFileName;
    }
}
