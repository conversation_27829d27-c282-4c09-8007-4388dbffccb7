package org.example.batchdemo.batch;

import org.example.batchdemo.exception.ValidationException;
import org.example.batchdemo.model.eigen.EigenBatch;
import org.example.batchdemo.model.eigen.EigenBatchDetail;
import org.example.batchdemo.model.eigen.EigenBatchHeader;
import org.example.batchdemo.model.eigen.EigenBatchTrailer;
import org.example.batchdemo.model.eigen.EigenFileHeader;
import org.example.batchdemo.model.eigen.EigenFileTrailer;
import org.example.batchdemo.model.eigen.EigenRootGroup;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class PaymentRouter {

    private static final Logger logger = LoggerFactory.getLogger(PaymentRouter.class);

    public Map<String, EigenRootGroup> routePayments(EigenRootGroup rootGroup) throws SQLException, ValidationException {
        logger.info("Routing started");

        if (rootGroup == null || rootGroup.getBatches() == null || rootGroup.getBatches().isEmpty()) {
            throw new ValidationException("Cannot route empty file.");
        }

        EigenBatch srcBatch = rootGroup.getBatches().get(0);
        List<EigenBatchDetail> details = srcBatch.getBatchDetails();

        // split payment types
        Map<String, List<EigenBatchDetail>> distributions = distributeDetailRecords(details);

        // map payment type/destination name to result keys
        Map<String, String> destKeyMap = new HashMap<>();
        destKeyMap.put("AX", "EigenRootGroupForSapAmex");
        destKeyMap.put("MC", "EigenRootGroupForSapMastercard");
        destKeyMap.put("VI", "EigenRootGroupForSapVisa");
        destKeyMap.put("DECLINED", "EigenRootGroupForSapDeclined");

        Map<String, EigenRootGroup> routedGroups = new HashMap<>();

        // create EigenRootGroup instances for each payment type/destination
        for (String paymentType : distributions.keySet()) {
            EigenRootGroup newRootGroup = generateStubRootGroup(rootGroup, paymentType);
            newRootGroup.getBatches().get(0).setBatchDetails(distributions.get(paymentType));
            updateTrailer(newRootGroup);
            
            // set result if and only if there is at least one detail record for the payment type
            if (newRootGroup.getFileTrailer().getTotalDetailLinesInFile() > 0) {
                routedGroups.put(destKeyMap.get(paymentType), newRootGroup);
                logger.info("Created route for payment type {} with {} payments", 
                    paymentType, newRootGroup.getFileTrailer().getTotalDetailLinesInFile());
            } else {
                logger.warn("no payments found for payment type {}", paymentType);
            }
        }
        
        logger.info("Routing complete");
        return routedGroups;
    }

    /**
     * Distributes detail records based on customer destination (i.e. PS or SAP),
     * and furthermore routes Amex, Mastercard, Visa, and declined payments for SAP
     * (+ unknown) customers.
     */
    private Map<String, List<EigenBatchDetail>> distributeDetailRecords(List<EigenBatchDetail> details) {
        Map<String, List<EigenBatchDetail>> distributions = new HashMap<>();
        distributions.put("AX", new ArrayList<>());
        distributions.put("MC", new ArrayList<>());
        distributions.put("VI", new ArrayList<>());
        distributions.put("DECLINED", new ArrayList<>());

        for (EigenBatchDetail detail : details) {
            String paymentType = determinePaymentType(detail);
            distributions.get(paymentType).add(detail);
        }

        return distributions;
    }

    private String determinePaymentType(EigenBatchDetail detail) {
        // Check if payment is declined
        if (isDeclinedPayment(detail)) {
            return "DECLINED";
        }

        // Route based on payment type
        String paymentType = detail.getPaymentType();
        if ("A".equals(paymentType)) {
            return "AX"; // American Express
        } else if ("M".equals(paymentType)) {
            return "MC"; // Mastercard
        } else if ("V".equals(paymentType)) {
            return "VI"; // Visa
        } else {
            // Unknown payment type goes to declined
            return "DECLINED";
        }
    }

    private boolean isDeclinedPayment(EigenBatchDetail detail) {
        // A payment is considered declined if it has an error code or response code indicating failure
        return (detail.getErrorCode() != null && !detail.getErrorCode().trim().isEmpty()) ||
               (detail.getResponseCode() != null && !"0000".equals(detail.getResponseCode().trim()));
    }

    private EigenRootGroup generateStubRootGroup(EigenRootGroup srcRootGroup, String paymentType) {
        EigenRootGroup newRootGroup = new EigenRootGroup();

        // Copy file header
        newRootGroup.setFileHeader(srcRootGroup.getFileHeader().copy());

        // Create new batch with copied headers
        EigenBatch newBatch = new EigenBatch();
        newBatch.setBatchHeader(srcRootGroup.getBatches().get(0).getBatchHeader().copy());
        newBatch.setBatchTrailer(srcRootGroup.getBatches().get(0).getBatchTrailer().copy());
        newBatch.setBatchDetails(new ArrayList<>());

        List<EigenBatch> batches = new ArrayList<>();
        batches.add(newBatch);
        newRootGroup.setBatches(batches);

        // Copy file trailer
        newRootGroup.setFileTrailer(srcRootGroup.getFileTrailer().copy());

        return newRootGroup;
    }

    private void updateTrailer(EigenRootGroup rootGroup) {
        EigenBatch batch = rootGroup.getBatches().get(0);
        List<EigenBatchDetail> details = batch.getBatchDetails();

        // Update batch trailer
        EigenBatchTrailer batchTrailer = batch.getBatchTrailer();
        batchTrailer.setTotalDetailLinesInBatch(details.size());
        
        BigDecimal batchAmount = details.stream()
            .map(EigenBatchDetail::getAmount)
            .filter(amount -> amount != null)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        batchTrailer.setTotalBatchAmount(batchAmount);

        // Update file trailer
        EigenFileTrailer fileTrailer = rootGroup.getFileTrailer();
        fileTrailer.setNumberOfBatches(1);
        fileTrailer.setTotalDetailLinesInFile(details.size());
        fileTrailer.setTotalFileAmount(batchAmount);
    }
}
