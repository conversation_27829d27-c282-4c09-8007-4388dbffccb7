package org.example.batchdemo.batch;

import org.example.batchdemo.model.eigen.EigenRootGroup;
import org.example.batchdemo.util.UuidGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class PaymentProcessor implements ItemProcessor<EigenRootGroup, EigenRootGroup> {

    private static final Logger logger = LoggerFactory.getLogger(PaymentProcessor.class);

    @Autowired
    private DuplicateFileChecker duplicateFileChecker;

    @Autowired
    private PaymentRouter paymentRouter;

    @Autowired
    private UuidGenerator uuidGenerator;

    @Override
    public EigenRootGroup process(EigenRootGroup item) throws Exception {
        logger.info("Payment processing started");

        // Generate tracking IDs
        String processId = uuidGenerator.generateUuid();
        String correlationId = uuidGenerator.generateUuid();
        String trackingId = processId + "_" + correlationId;

        logger.info("Processing with tracking ID: {}", trackingId);

        // Check for duplicate files
        String fileName = "opac_request_" + System.currentTimeMillis() + ".txt"; // Simulated filename
        duplicateFileChecker.checkForDuplicates(fileName, item);

        // Route payments by type
        Map<String, EigenRootGroup> routedGroups = paymentRouter.routePayments(item);

        // For now, we'll return the original item
        // In a more complex implementation, we might process each routed group separately
        logger.info("Payment processing completed. Routed into {} groups", routedGroups.size());

        return item;
    }
}
