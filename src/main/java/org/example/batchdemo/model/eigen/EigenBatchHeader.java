package org.example.batchdemo.model.eigen;

import java.util.Date;

public class EigenBatchHeader {

    private String recordType = "B";
    private Date processedDate;
    private long obSeqNumber;
    private long ebSeqNumber;
    private String batchDCode;
    private String echoDataBatch;

    public String getRecordType() {
        return recordType;
    }

    public void setRecordType(String recordType) {
        this.recordType = recordType;
    }

    public Date getProcessedDate() {
        return processedDate;
    }

    public void setProcessedDate(Date processedDate) {
        this.processedDate = processedDate;
    }

    public long getObSeqNumber() {
        return obSeqNumber;
    }

    public void setObSeqNumber(long obSeqNumber) {
        this.obSeqNumber = obSeqNumber;
    }

    public long getEbSeqNumber() {
        return ebSeqNumber;
    }

    public void setEbSeqNumber(long ebSeqNumber) {
        this.ebSeqNumber = ebSeqNumber;
    }

    public String getBatchDCode() {
        return batchDCode;
    }

    public void setBatchDCode(String batchDCode) {
        this.batchDCode = batchDCode;
    }

    public String getEchoDataBatch() {
        return echoDataBatch;
    }

    public void setEchoDataBatch(String echoDataBatch) {
        this.echoDataBatch = echoDataBatch;
    }

    @Override
    public String toString() {
        return "EigenBatchHeader{" +
                "recordType=" + recordType +
                ", processedDate=" + processedDate +
                ", obSeqNumber=" + obSeqNumber +
                ", ebSeqNumber=" + ebSeqNumber +
                ", batchDCode='" + batchDCode + '\'' +
                ", echoDataBatch='" + echoDataBatch + '\'' +
                '}';
    }

    public EigenBatchHeader copy() {
        EigenBatchHeader eigenBatchHeader = new EigenBatchHeader();
        eigenBatchHeader.setRecordType(this.getRecordType());
        eigenBatchHeader.setProcessedDate(this.getProcessedDate());
        eigenBatchHeader.setObSeqNumber(this.getObSeqNumber());
        eigenBatchHeader.setEbSeqNumber(this.getEbSeqNumber());
        eigenBatchHeader.setBatchDCode(this.getBatchDCode());
        eigenBatchHeader.setEchoDataBatch(this.getEchoDataBatch());
        return eigenBatchHeader;
    }
}
