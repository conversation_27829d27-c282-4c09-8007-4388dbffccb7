package org.example.batchdemo.model.eigen;

import java.math.BigDecimal;

public class EigenFileTrailer {

    private String recordType = "R";
    private long ofSeqNumber;
    private long efSeqNumber;
    private long numberOfBatches;
    private long totalDetailLinesInFile;
    private BigDecimal totalFileAmount;

    public String getRecordType() {
        return recordType;
    }

    public void setRecordType(String recordType) {
        this.recordType = recordType;
    }

    public long getOfSeqNumber() {
        return ofSeqNumber;
    }

    public void setOfSeqNumber(long ofSeqNumber) {
        this.ofSeqNumber = ofSeqNumber;
    }

    public long getEfSeqNumber() {
        return efSeqNumber;
    }

    public void setEfSeqNumber(long efSeqNumber) {
        this.efSeqNumber = efSeqNumber;
    }

    public long getNumberOfBatches() {
        return numberOfBatches;
    }

    public void setNumberOfBatches(long numberOfBatches) {
        this.numberOfBatches = numberOfBatches;
    }

    public long getTotalDetailLinesInFile() {
        return totalDetailLinesInFile;
    }

    public void setTotalDetailLinesInFile(long totalDetailLinesInFile) {
        this.totalDetailLinesInFile = totalDetailLinesInFile;
    }

    public BigDecimal getTotalFileAmount() {
        return totalFileAmount;
    }

    public void setTotalFileAmount(BigDecimal totalFileAmount) {
        this.totalFileAmount = totalFileAmount;
    }

    @Override
    public String toString() {
        return "EigenFileTrailer{" +
                "recordType=" + recordType +
                ", ofSeqNumber=" + ofSeqNumber +
                ", efSeqNumber=" + efSeqNumber +
                ", numberOfBatches=" + numberOfBatches +
                ", totalDetailLinesInFile=" + totalDetailLinesInFile +
                ", totalFileAmount=" + totalFileAmount +
                '}';
    }

    public EigenFileTrailer copy() {
        EigenFileTrailer eigenFileTrailer = new EigenFileTrailer();
        eigenFileTrailer.setRecordType(this.getRecordType());
        eigenFileTrailer.setOfSeqNumber(this.getOfSeqNumber());
        eigenFileTrailer.setEfSeqNumber(this.getEfSeqNumber());
        eigenFileTrailer.setNumberOfBatches(this.getNumberOfBatches());
        eigenFileTrailer.setTotalDetailLinesInFile(this.getTotalDetailLinesInFile());
        eigenFileTrailer.setTotalFileAmount(this.getTotalFileAmount()); // BigDecimal is immutable
        return eigenFileTrailer;
    }
}
