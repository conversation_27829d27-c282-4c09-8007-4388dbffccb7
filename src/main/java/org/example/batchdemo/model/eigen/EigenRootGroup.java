package org.example.batchdemo.model.eigen;

import java.util.List;

public class EigenRootGroup {

    private EigenFileHeader fileHeader;
    private List<EigenBatch> batches;
    private EigenFileTrailer fileTrailer;

    public EigenFileHeader getFileHeader() {
        return fileHeader;
    }

    public void setFileHeader(EigenFileHeader fileHeader) {
        this.fileHeader = fileHeader;
    }

    public List<EigenBatch> getBatches() {
        return batches;
    }

    public void setBatches(List<EigenBatch> batches) {
        this.batches = batches;
    }

    public EigenFileTrailer getFileTrailer() {
        return fileTrailer;
    }

    public void setFileTrailer(EigenFileTrailer fileTrailer) {
        this.fileTrailer = fileTrailer;
    }

    @Override
    public String toString() {
        return "EigenRootGroup{" +
                "fileHeader=" + fileHeader +
                ", batches=" + batches +
                ", fileTrailer=" + fileTrailer +
                '}';
    }
}
