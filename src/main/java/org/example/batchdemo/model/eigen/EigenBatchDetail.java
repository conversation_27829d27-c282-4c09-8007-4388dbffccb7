package org.example.batchdemo.model.eigen;

import java.math.BigDecimal;

public class EigenBatchDetail {

    private String recordType = "D";
    private String odSeqNumber;
    private String processedCode;
    private String errorCode;
    private String errorField;
    private String errorDesc;
    private String responseCode;
    private String eiSeqNumber;
    private String termId;
    private String transCode;
    private String track2Acc;
    private BigDecimal amount;
    private BigDecimal amount2;
    private String approvalCd;
    private String opId;
    private String invoiceNum;
    private String extOpId;
    private String clientId;
    private String custId;
    private String paymentType;
    private BigDecimal amount3;
    private BigDecimal taxAmount;
    private String paymentSource;
    private String referenceNumber;

    public String getRecordType() {
        return recordType;
    }

    public void setRecordType(String recordType) {
        this.recordType = recordType;
    }

    public String getOdSeqNumber() {
        return odSeqNumber;
    }

    public void setOdSeqNumber(String odSeqNumber) {
        this.odSeqNumber = odSeqNumber;
    }

    public String getProcessedCode() {
        return processedCode;
    }

    public void setProcessedCode(String processedCode) {
        this.processedCode = processedCode;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorField() {
        return errorField;
    }

    public void setErrorField(String errorField) {
        this.errorField = errorField;
    }

    public String getErrorDesc() {
        return errorDesc;
    }

    public void setErrorDesc(String errorDesc) {
        this.errorDesc = errorDesc;
    }

    public String getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }

    public String getEiSeqNumber() {
        return eiSeqNumber;
    }

    public void setEiSeqNumber(String eiSeqNumber) {
        this.eiSeqNumber = eiSeqNumber;
    }

    public String getTermId() {
        return termId;
    }

    public void setTermId(String termId) {
        this.termId = termId;
    }

    public String getTransCode() {
        return transCode;
    }

    public void setTransCode(String transCode) {
        this.transCode = transCode;
    }

    public String getTrack2Acc() {
        return track2Acc;
    }

    public void setTrack2Acc(String track2Acc) {
        this.track2Acc = track2Acc;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getAmount2() {
        return amount2;
    }

    public void setAmount2(BigDecimal amount2) {
        this.amount2 = amount2;
    }

    public String getApprovalCd() {
        return approvalCd;
    }

    public void setApprovalCd(String approvalCd) {
        this.approvalCd = approvalCd;
    }

    public String getOpId() {
        return opId;
    }

    public void setOpId(String opId) {
        this.opId = opId;
    }

    public String getInvoiceNum() {
        return invoiceNum;
    }

    public void setInvoiceNum(String invoiceNum) {
        this.invoiceNum = invoiceNum;
    }

    public String getExtOpId() {
        return extOpId;
    }

    public void setExtOpId(String extOpId) {
        this.extOpId = extOpId;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public BigDecimal getAmount3() {
        return amount3;
    }

    public void setAmount3(BigDecimal amount3) {
        this.amount3 = amount3;
    }

    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

    public String getPaymentSource() {
        return paymentSource;
    }

    public void setPaymentSource(String paymentSource) {
        this.paymentSource = paymentSource;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    @Override
    public String toString() {
        return "EigenBatchDetail{" +
                "recordType=" + recordType +
                ", odSeqNumber=" + odSeqNumber +
                ", processedCode=" + processedCode +
                ", errorCode='" + errorCode + '\'' +
                ", errorField='" + errorField + '\'' +
                ", errorDesc='" + errorDesc + '\'' +
                ", responseCode=" + responseCode +
                ", eiSeqNumber=" + eiSeqNumber +
                ", termId='" + termId + '\'' +
                ", transCode='" + transCode + '\'' +
                ", track2Acc='" + track2Acc + '\'' +
                ", amount=" + amount +
                ", amount2=" + amount2 +
                ", approvalCd='" + approvalCd + '\'' +
                ", opId='" + opId + '\'' +
                ", invoiceNum='" + invoiceNum + '\'' +
                ", extOpId='" + extOpId + '\'' +
                ", clientId='" + clientId + '\'' +
                ", custId='" + custId + '\'' +
                ", paymentType='" + paymentType + '\'' +
                ", amount3=" + amount3 +
                ", taxAmount=" + taxAmount +
                ", paymentSource='" + paymentSource + '\'' +
                ", referenceNumber='" + referenceNumber + '\'' +
                '}';
    }
}
