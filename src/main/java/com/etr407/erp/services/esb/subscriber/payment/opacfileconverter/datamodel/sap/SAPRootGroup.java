package com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.sap;

import java.util.List;

public class SAPRootGroup {

	private BFKKZGR00 BFKKZGR00;
	private BFKKZK BFKKZK;
	private List<BFKKZP> BFKKZPList;

	public BFKKZGR00 getBFKKZGR00() {
		return BFKKZGR00;
	}

	public void setBFKKZGR00(BFKKZGR00 bFKKZGR00) {
		BFKKZGR00 = bFKKZGR00;
	}

	public BFKKZK getBFKKZK() {
		return BFKKZK;
	}

	public void setBFKKZK(BFKKZK bFKKZK) {
		BFKKZK = bFKKZK;
	}

	public List<BFKKZP> getBFKKZPList() {
		return BFKKZPList;
	}

	public void setBFKKZPList(List<BFKKZP> bFKKZPList) {
		BFKKZPList = bFKKZPList;
	}

	@Override
	public String toString() {
		return "SAPRootGroup [BFKKZGR00=" + BFKKZGR00 + ", BFKKZK=" + BF<PERSON>KZK + ", BF<PERSON>KZPList=" + BF<PERSON><PERSON>ZPList + "]";
	}
}
