package com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.mapping;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenBatch;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenBatchDetail;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenRootGroup;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.sap.BFKKZGR00;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.sap.BFKKZK;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.sap.BFKKZP;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.sap.SAPRootGroup;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.exception.ValidationException;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.utils.SAPUtils;

public class EigenToSapMapper implements Processor {

	private static final Logger LOGGER = LoggerFactory.getLogger(EigenToSapMapper.class);
	private static final DateFormat processDateFormat = new SimpleDateFormat("yyyyMMdd");

	private final String sapClient;

	public EigenToSapMapper(String sapClient) {
		this.sapClient = sapClient;
	}

	/**
	 * Sets the exchange body to a mapped {@code List<SAPRootGroup>} instance when a
	 * valid mapping is produced, otherwise {@code null}.
	 * 
	 * @param ex an {@code Exchange} instance
	 * @throws ParseException when the {@code processDate} exchange property is
	 *                        invalid
	 */
	public void process(Exchange ex) throws ParseException, ValidationException {
		LOGGER.info("mapping process started");

		@SuppressWarnings("unchecked")
		List<EigenRootGroup> body = (List<EigenRootGroup>) ex.getIn().getBody();

		if (body == null || body.size() == 0) {
			ex.getIn().setBody(null);
			return;
		}

		EigenRootGroup eigenRootGroup = body.get(0);
		List<EigenBatch> batches = eigenRootGroup.getBatches();
		String lotId = (String) ex.getProperty("sapLotId");
		String cardType = SAPUtils.getCardType(batches);
		Date processDate = processDateFormat.parse((String) ex.getProperty("processDate"));

		// SAP header
		BFKKZGR00 sapHeader = new BFKKZGR00();
		sapHeader.setMANDT(this.sapClient);

		// SAP header data
		BFKKZK sapHeaderData = new BFKKZK();
		sapHeaderData.setKEYZ1(lotId);
		sapHeaderData.setKEYZ2("PAC " + cardType + " Payments");
		sapHeaderData.setBLART(cardType);
		sapHeaderData.setBUDAT(processDate);
		sapHeaderData.setBLDAT(eigenRootGroup.getFileHeader().getSubmissionDate());
		sapHeaderData.setVALUT(eigenRootGroup.getFileHeader().getSubmissionDate());
		// SAP items
		List<BFKKZP> sapItemList = new ArrayList<>();
		for (EigenBatch eigenBatch : eigenRootGroup.getBatches()) {
			for (EigenBatchDetail eigenBatchDetail : eigenBatch.getBatchDetails()) {
				BFKKZP sapItem = new BFKKZP();
				sapItem.setSELW1(eigenBatchDetail.getInvoiceNum());
				sapItem.setBETRZ(eigenBatchDetail.getAmount());
				sapItem.setBLART(cardType);
				sapItem.setBUDAT(processDate);
				sapItem.setBLDAT(eigenRootGroup.getFileHeader().getSubmissionDate());
				sapItem.setVALUT(eigenRootGroup.getFileHeader().getSubmissionDate());
				sapItem.setINFOF("ETRPAC" + eigenBatchDetail.getCustId());
				sapItem.setAUNUM(eigenBatchDetail.getApprovalCd());

				sapItemList.add(sapItem);

			}
		}

		// SAP root
		SAPRootGroup sapRootGroup = new SAPRootGroup();
		sapRootGroup.setBFKKZGR00(sapHeader);
		sapRootGroup.setBFKKZK(sapHeaderData);
		sapRootGroup.setBFKKZPList(sapItemList);

		// set debit total and item count in header data
		BigDecimal sapDebitTotal = sapItemList.stream().filter(x -> x.getBETRZ().compareTo(BigDecimal.ZERO) >= 0).map(BFKKZP::getBETRZ).reduce(BigDecimal.ZERO, BigDecimal::add);
		sapHeaderData.setKTSUS(sapDebitTotal);

		sapHeaderData.setKSUMP(sapItemList.size());

		LOGGER.info("mapping process ended");

		List<SAPRootGroup> newBody = new ArrayList<>();
		newBody.add(sapRootGroup);
		ex.getIn().setBody(newBody);
	}

}
