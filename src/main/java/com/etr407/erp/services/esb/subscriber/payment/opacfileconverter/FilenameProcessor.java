package com.etr407.erp.services.esb.subscriber.payment.opacfileconverter;

import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.apache.cxf.attachment.ContentDisposition;

import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.exception.MissingFilenameException;

/**
 * Extracts the filename portion of an HTTP Content-Disposition header.
 */
public class FilenameProcessor implements Processor {

	@Override
	public void process(Exchange exchange) throws MissingFilenameException {
		String contentDispositionHeader = (String) exchange.getIn().getHeader("Content-Disposition");
		if (contentDispositionHeader == null) {
			throw new MissingFilenameException("No Content-Disposition header found");
		}
		ContentDisposition cd = new ContentDisposition(contentDispositionHeader);
		String filename = cd.getParameter("filename");

		if (filename == null || "".equals(filename.trim())) {
			throw new MissingFilenameException("No valid filename found in the Content-Disposition header");
		}
		exchange.getIn().setHeader(Exchange.FILE_NAME, filename);
	}
}
