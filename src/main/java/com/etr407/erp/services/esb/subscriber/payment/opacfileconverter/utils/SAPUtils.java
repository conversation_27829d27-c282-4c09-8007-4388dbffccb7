package com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.utils;

import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenBatch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

public class SAPUtils {

	private static final Logger LOGGER = LoggerFactory.getLogger(SAPUtils.class);

	/**
	 * Utility method to retrieve the card type from the eigen records
	 * @param batches contains a list of EigenBatch elements
	 * @return cardType
	 */
	public static String getCardType(List<EigenBatch> batches) {
		LOGGER.info("SAPUtils.getCardType method started");
		String cardType = batches.get(0).getBatchDetails().get(0).getExtOpId().trim().substring(0, 2).toUpperCase();
		LOGGER.info("SAPUtils.getCardType method ended");
		return cardType;
	}

}
