package com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.mapping;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenBatch;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenBatchDetail;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenRootGroup;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.sap.declined.BFKKRK;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.sap.declined.BFKKRP;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.sap.declined.BFKKZGR00;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.sap.declined.SAPDeclinedRootGroup;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.exception.ValidationException;

public class EigenToSapDeclinedMapper implements Processor {

	private static final Logger LOGGER = LoggerFactory.getLogger(EigenToSapDeclinedMapper.class);
	private static final DateFormat processDateFormat = new SimpleDateFormat("yyyyMMdd");
	private static final HashMap<String, String> RESPONSE_CODE_MAP = new HashMap<String, String>() {
		private static final long serialVersionUID = 1L;
		{
			put("900", "C00");
			put("901", "C01");
			put("902", "C02");
			put("903", "C03");
			put("904", "C04");
			put("905", "C05");
			put("906", "C06");
			put("907", "C07");
			put("908", "C08");
			put("909", "C09");
			put("991", "C91");
			put("999", "C99");
		}
	};

	private final String sapClient;

	public EigenToSapDeclinedMapper(String sapClient) {
		this.sapClient = sapClient;
	}

	/**
	 * Sets the exchange body to a mapped {@code List<SAPRootGroup>} instance when a
	 * valid mapping is produced, otherwise {@code null}.
	 * 
	 * @param ex an {@code Exchange} instance
	 * @throws ParseException when the {@code processDate} exchange property is
	 *                        invalid
	 */
	public void process(Exchange ex) throws ParseException, ValidationException {
		LOGGER.info("mapping process started");

		@SuppressWarnings("unchecked")
		List<EigenRootGroup> body = (List<EigenRootGroup>) ex.getIn().getBody();

		if (body == null || body.size() == 0) {
			ex.getIn().setBody(null);
			return;
		}

		EigenRootGroup eigenRootGroup = body.get(0);
		String lotId = (String) ex.getProperty("sapLotId");
		Date processDate = processDateFormat.parse((String) ex.getProperty("processDate"));

		// SAP header
		BFKKZGR00 sapHeader = new BFKKZGR00();
		sapHeader.setMANDT(this.sapClient);

		// SAP header data
		BFKKRK sapHeaderData = new BFKKRK();
		sapHeaderData.setKEYR1(lotId);
		sapHeaderData.setKEYR2("PAC Declines");
		sapHeaderData.setBUDAT(processDate);
		sapHeaderData.setBLDAT(eigenRootGroup.getFileHeader().getSubmissionDate());
		sapHeaderData.setVALUT(eigenRootGroup.getFileHeader().getSubmissionDate());
		// SAP items
		List<BFKKRP> sapItemList = new ArrayList<>();
		for (EigenBatch eigenBatch : eigenRootGroup.getBatches()) {
			for (EigenBatchDetail eigenBatchDetail : eigenBatch.getBatchDetails()) {
				BFKKRP sapItem = new BFKKRP();
				sapItem.setSELW1(eigenBatchDetail.getInvoiceNum());
				// prepend negative sign as this amount is a Return amount
				sapItem.setBETRR(eigenBatchDetail.getAmount().movePointLeft(2).setScale(2).negate());
				sapItem.setBUDAT(processDate);
				sapItem.setBLDAT(eigenRootGroup.getFileHeader().getSubmissionDate());
				sapItem.setVALUT(eigenRootGroup.getFileHeader().getSubmissionDate());
				sapItem.setRLGRD(mapResponseCode(eigenBatchDetail.getResponseCode()));
				sapItemList.add(sapItem);
			}
		}

		// SAP root
		SAPDeclinedRootGroup sapDeclinedRootGroup = new SAPDeclinedRootGroup();
		sapDeclinedRootGroup.setBFKKZGR00(sapHeader);
		sapDeclinedRootGroup.setBFKKRK(sapHeaderData);
		sapDeclinedRootGroup.setBFKKRPList(sapItemList);

		// Header field KSUMH needs to be a positive value, only items should reflect as
		// negative
		sapHeaderData.setKSUMH(sapItemList.stream().map(BFKKRP::getBETRR).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2).negate());
		sapHeaderData.setKSUMP(sapItemList.size());

		List<SAPDeclinedRootGroup> newBody = new ArrayList<>();
		newBody.add(sapDeclinedRootGroup);
		ex.getIn().setBody(newBody);

		LOGGER.info("mapping process ended");
	}

	private String mapResponseCode(String responseCode) {
		return RESPONSE_CODE_MAP.getOrDefault(responseCode, responseCode);
	}
}
