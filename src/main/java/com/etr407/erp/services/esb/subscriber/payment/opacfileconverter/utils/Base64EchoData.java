package com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.utils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.Base64;

public class Base64EchoData {
	private final long totalRecords;
	private final long totalAmount;

	/**
	 * Create instance from values.
	 */
	public Base64EchoData(long totalRecords, long totalAmount) {
		this.totalRecords = totalRecords;
		this.totalAmount = totalAmount;
	}

	public long getTotalRecords() {
		return totalRecords;
	}

	public long getTotalAmount() {
		return totalAmount;
	}

	/**
	 * Create instance from an existing base64 string.
	 */
	public Base64EchoData(String b64String) throws IllegalArgumentException {
		String trimmedB64String = b64String.trim();
		byte[] arr = Base64.getDecoder().decode(trimmedB64String);
		if (arr.length != 11) {
			String msg = String.format("Base64 string must be 11 bytes (got %d bytes from string of length %d: '%s'}.", arr.length, trimmedB64String.length(), trimmedB64String);
			throw new IllegalArgumentException(msg);
		}
		this.totalRecords = fromByteArray(arr, 0, 4);
		this.totalAmount = fromByteArray(arr, 4, 7);
	}

	public String toString() {
		ByteArrayOutputStream bos = new ByteArrayOutputStream();
		try {
			bos.write(toByteArray(this.totalRecords, 4)); // need max 4 bytes for 8 digits
			bos.write(toByteArray(this.totalAmount, 7)); // need max 7 bytes for 16 digits
		} catch (IOException e) {
			return "[INVALID]";
		}
		byte[] arr = bos.toByteArray();
		return Base64.getEncoder().encodeToString(arr);
	}

	/**
	 * Store a long in big-endian order in a byte array.
	 *
	 * @param num The number to split into bytes.
	 * @param len Number of bytes to be allocated.
	 * @return A byte array representing the number.
	 */
	private byte[] toByteArray(long num, int len) {
		byte[] arr = new byte[len];
		for (int i = 0; i < len; i++) {
			int shift = (len - i - 1) * 8;
			arr[i] = (byte) (num >> shift);
		}
		return arr;
	}

	/**
	 * Extract a long from a range in a byte array in big-endian order.
	 *
	 * @param arr    A byte array containing multiple numbers.
	 * @param offset The starting index.
	 * @param len    The number of bytes allocated to the number in the array.
	 * @return The extracted number.
	 */
	private long fromByteArray(byte[] arr, int offset, int len) {
		byte[] slice = Arrays.copyOfRange(arr, offset, offset + len);
		long num = 0L;
		for (int i = 0; i < len; i++) {
			num <<= 8;
			long masked = 0xFFL & slice[i]; // prevent sign conversion
			num |= masked;
		}
		return num;
	}

	public boolean equals(Object obj) {
		if (this.getClass().equals(obj.getClass())) {
			Base64EchoData that = (Base64EchoData) obj;

			if (this.totalAmount == that.totalAmount && this.totalRecords == that.totalRecords) {
				return true;
			}
		}
		return false;
	}

}
