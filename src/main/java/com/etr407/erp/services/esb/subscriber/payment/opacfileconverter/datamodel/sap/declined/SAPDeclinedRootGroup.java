package com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.sap.declined;

import java.util.List;

public class SAPDeclinedRootGroup {

	private BFKKZGR00 BFKKZGR00;
	private BFKKRK BFKKRK;
	private List<BFKKRP> BFKKRPList;

	public BFKKZGR00 getBFKKZGR00() {
		return BFKKZGR00;
	}

	public void setBFKKZGR00(BFKKZGR00 bFKKZGR00) {
		BFKKZGR00 = bFKKZGR00;
	}

	public BFKKRK getBFKKRK() {
		return BFKKRK;
	}

	public void setBFKKRK(BFKKRK bFKKRK) {
		BFKKRK = bFKKRK;
	}

	public List<BFKKRP> getBFKKRPList() {
		return BFKKRPList;
	}

	public void setBFKKRPList(List<BFKKRP> bFKKRPList) {
		BFKKRPList = bFKKRPList;
	}

	@Override
	public String toString() {
		return "SAPRootGroup [BFKKZGR00=" + BFKKZGR00 + ", BFKKRK=" + BFKKRK + ", BFKKRPList=" + BFKKRPList + "]";
	}

}
