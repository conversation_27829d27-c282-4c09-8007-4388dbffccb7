package com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.sap.declined;

import java.math.BigDecimal;
import java.util.Date;

public class BFKKRK {

	String STYPE = "1";
	String TBNAM;
	String KEYR1;
	String KEYR2;
	String FIKEY;
	String RLSKO;
	String BUKRS;
	String GSBER;
	String BLART;
	String WAERS;
	String KURSF;
	Date BUDAT;
	Date BLDAT;
	Date VALUT;
	String XEIPH;
	String AUGRD;
	String XRLSD;
	String XRLSK;
	String XSTEB;
	String XRLSB;
	String BANKL;
	String BANKK;
	String BANKN;
	String HBKID;
	String HKTID;
	String XCALCGEB;
	String XACCEPTCHARGES;
	String RLMOD;
	BigDecimal KSUMS;
	BigDecimal KSUMH;
	int KSUMP;
	String XERWR;
	String PRCTR;

	public String getSTYPE() {
		return STYPE;
	}

	public void setSTYPE(String sTYPE) {
		STYPE = sTYPE;
	}

	public String getTBNAM() {
		return TBNAM;
	}

	public void setTBNAM(String tBNAM) {
		TBNAM = tBNAM;
	}

	public String getKEYR1() {
		return KEYR1;
	}

	public void setKEYR1(String kEYR1) {
		KEYR1 = kEYR1;
	}

	public String getKEYR2() {
		return KEYR2;
	}

	public void setKEYR2(String kEYR2) {
		KEYR2 = kEYR2;
	}

	public String getFIKEY() {
		return FIKEY;
	}

	public void setFIKEY(String fIKEY) {
		FIKEY = fIKEY;
	}

	public String getRLSKO() {
		return RLSKO;
	}

	public void setRLSKO(String rLSKO) {
		RLSKO = rLSKO;
	}

	public String getBUKRS() {
		return BUKRS;
	}

	public void setBUKRS(String bUKRS) {
		BUKRS = bUKRS;
	}

	public String getGSBER() {
		return GSBER;
	}

	public void setGSBER(String gSBER) {
		GSBER = gSBER;
	}

	public String getBLART() {
		return BLART;
	}

	public void setBLART(String bLART) {
		BLART = bLART;
	}

	public String getWAERS() {
		return WAERS;
	}

	public void setWAERS(String wAERS) {
		WAERS = wAERS;
	}

	public String getKURSF() {
		return KURSF;
	}

	public void setKURSF(String kURSF) {
		KURSF = kURSF;
	}

	public Date getBUDAT() {
		return BUDAT;
	}

	public void setBUDAT(Date bUDAT) {
		BUDAT = bUDAT;
	}

	public Date getBLDAT() {
		return BLDAT;
	}

	public void setBLDAT(Date bLDAT) {
		BLDAT = bLDAT;
	}

	public Date getVALUT() {
		return VALUT;
	}

	public void setVALUT(Date vALUT) {
		VALUT = vALUT;
	}

	public String getXEIPH() {
		return XEIPH;
	}

	public void setXEIPH(String xEIPH) {
		XEIPH = xEIPH;
	}

	public String getAUGRD() {
		return AUGRD;
	}

	public void setAUGRD(String aUGRD) {
		AUGRD = aUGRD;
	}

	public String getXRLSD() {
		return XRLSD;
	}

	public void setXRLSD(String xRLSD) {
		XRLSD = xRLSD;
	}

	public String getXRLSK() {
		return XRLSK;
	}

	public void setXRLSK(String xRLSK) {
		XRLSK = xRLSK;
	}

	public String getXSTEB() {
		return XSTEB;
	}

	public void setXSTEB(String xSTEB) {
		XSTEB = xSTEB;
	}

	public String getXRLSB() {
		return XRLSB;
	}

	public void setXRLSB(String xRLSB) {
		XRLSB = xRLSB;
	}

	public String getBANKL() {
		return BANKL;
	}

	public void setBANKL(String bANKL) {
		BANKL = bANKL;
	}

	public String getBANKK() {
		return BANKK;
	}

	public void setBANKK(String bANKK) {
		BANKK = bANKK;
	}

	public String getBANKN() {
		return BANKN;
	}

	public void setBANKN(String bANKN) {
		BANKN = bANKN;
	}

	public String getHBKID() {
		return HBKID;
	}

	public void setHBKID(String hBKID) {
		HBKID = hBKID;
	}

	public String getHKTID() {
		return HKTID;
	}

	public void setHKTID(String hKTID) {
		HKTID = hKTID;
	}

	public String getXCALCGEB() {
		return XCALCGEB;
	}

	public void setXCALCGEB(String xCALCGEB) {
		XCALCGEB = xCALCGEB;
	}

	public String getXACCEPTCHARGES() {
		return XACCEPTCHARGES;
	}

	public void setXACCEPTCHARGES(String xACCEPTCHARGES) {
		XACCEPTCHARGES = xACCEPTCHARGES;
	}

	public String getRLMOD() {
		return RLMOD;
	}

	public void setRLMOD(String rLMOD) {
		RLMOD = rLMOD;
	}

	public BigDecimal getKSUMS() {
		return KSUMS;
	}

	public void setKSUMS(BigDecimal kSUMS) {
		KSUMS = kSUMS;
	}

	public BigDecimal getKSUMH() {
		return KSUMH;
	}

	public void setKSUMH(BigDecimal kSUMH) {
		KSUMH = kSUMH;
	}

	public int getKSUMP() {
		return KSUMP;
	}

	public void setKSUMP(int kSUMP) {
		KSUMP = kSUMP;
	}

	public String getXERWR() {
		return XERWR;
	}

	public void setXERWR(String xERWR) {
		XERWR = xERWR;
	}

	public String getPRCTR() {
		return PRCTR;
	}

	public void setPRCTR(String pRCTR) {
		PRCTR = pRCTR;
	}

	@Override
	public String toString() {
		return "BFKKRK [STYPE=" + STYPE + ", TBNAM=" + TBNAM + ", KEYR1=" + KEYR1 + ", KEYR2=" + KEYR2 + ", FIKEY=" + FIKEY + ", RLSKO=" + RLSKO + ", BUKRS=" + BUKRS + ", GSBER=" + GSBER + ", BLART=" + BLART + ", WAERS=" + WAERS
				+ ", KURSF=" + KURSF + ", BUDAT=" + BUDAT + ", BLDAT=" + BLDAT + ", VALUT=" + VALUT + ", XEIPH=" + XEIPH + ", AUGRD=" + AUGRD + ", XRLSD=" + XRLSD + ", XRLSK=" + XRLSK + ", XSTEB=" + XSTEB + ", XRLSB=" + XRLSB + ", BANKL="
				+ BANKL + ", BANKK=" + BANKK + ", BANKN=" + BANKN + ", HBKID=" + HBKID + ", HKTID=" + HKTID + ", XCALCGEB=" + XCALCGEB + ", XACCEPTCHARGES=" + XACCEPTCHARGES + ", RLMOD=" + RLMOD + ", KSUMS=" + KSUMS + ", KSUMH=" + KSUMH
				+ ", KSUMP=" + KSUMP + ", XERWR=" + XERWR + ", PRCTR=" + PRCTR + "]";
	}

}
