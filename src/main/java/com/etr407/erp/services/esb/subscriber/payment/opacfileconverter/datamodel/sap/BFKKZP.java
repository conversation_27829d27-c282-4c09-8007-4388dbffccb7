package com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.sap;

import java.math.BigDecimal;
import java.util.Date;

public class BFKKZP {

	private String STYPE = "2";
	private String TBNAM;
	private String SELT1;
	private String SELT2;
	private String SELT3;
	private String SELW1;
	private String SELW2;
	private String SELW3;
	private BigDecimal BETRZ;
	private BigDecimal BETRH;
	private BigDecimal TBETR;
	private String BVRKO;
	private String BUKRS;
	private String GSBER;
	private String BLART;
	private String WAERS;
	private String KURSF;
	private Date BUDAT;
	private Date BLDAT;
	private Date VALUT;
	private String XEIPH;
	private String AUGRD;
	private String XAKON;
	private String XKLAE;
	private String KLAEH;
	private String TXTVW;
	private String CHCKN;
	private String BANKS;
	private String BANKL;
	private String BANKN;
	private String BKONT;
	private String KOINH;
	private String XPGRO;
	private String XDAUB;
	private String INFOF;
	private String KUKON;
	private String BKREF;
	private String CCINS;
	private String CCNUM;
	private Date DATAB;
	private Date DATBI;
	private String AUNUM;
	private Date AUDAT;
	private String AUTIM;
	private String PRCTR;
	private String BEGRU;
	private String MERCH;
	private String IBAN;
	private String SWIFT;
	private String ESNUM;
	private String LNKID;

	/**
	 * @return the sTYPE
	 */
	public String getSTYPE() {
		return STYPE;
	}

	/**
	 * @param sTYPE
	 *            the sTYPE to set
	 */
	public void setSTYPE(String sTYPE) {
		STYPE = sTYPE;
	}

	/**
	 * @return the tBNAM
	 */
	public String getTBNAM() {
		return TBNAM;
	}

	/**
	 * @param tBNAM
	 *            the tBNAM to set
	 */
	public void setTBNAM(String tBNAM) {
		TBNAM = tBNAM;
	}

	/**
	 * @return the sELT1
	 */
	public String getSELT1() {
		return SELT1;
	}

	/**
	 * @param sELT1
	 *            the sELT1 to set
	 */
	public void setSELT1(String sELT1) {
		SELT1 = sELT1;
	}

	/**
	 * @return the sELT2
	 */
	public String getSELT2() {
		return SELT2;
	}

	/**
	 * @param sELT2
	 *            the sELT2 to set
	 */
	public void setSELT2(String sELT2) {
		SELT2 = sELT2;
	}

	/**
	 * @return the sELT3
	 */
	public String getSELT3() {
		return SELT3;
	}

	/**
	 * @param sELT3
	 *            the sELT3 to set
	 */
	public void setSELT3(String sELT3) {
		SELT3 = sELT3;
	}

	/**
	 * @return the sELW1
	 */
	public String getSELW1() {
		return SELW1;
	}

	/**
	 * @param sELW1
	 *            the sELW1 to set
	 */
	public void setSELW1(String sELW1) {
		SELW1 = sELW1;
	}

	/**
	 * @return the sELW2
	 */
	public String getSELW2() {
		return SELW2;
	}

	/**
	 * @param sELW2
	 *            the sELW2 to set
	 */
	public void setSELW2(String sELW2) {
		SELW2 = sELW2;
	}

	/**
	 * @return the sELW3
	 */
	public String getSELW3() {
		return SELW3;
	}

	/**
	 * @param sELW3
	 *            the sELW3 to set
	 */
	public void setSELW3(String sELW3) {
		SELW3 = sELW3;
	}

	/**
	 * @return the bETRZ
	 */
	public BigDecimal getBETRZ() {
		return BETRZ;
	}

	/**
	 * @param bETRZ
	 *            the bETRZ to set
	 */
	public void setBETRZ(BigDecimal bETRZ) {
		BETRZ = bETRZ;
	}

	/**
	 * @return the bETRH
	 */
	public BigDecimal getBETRH() {
		return BETRH;
	}

	/**
	 * @param bETRH
	 *            the bETRH to set
	 */
	public void setBETRH(BigDecimal bETRH) {
		BETRH = bETRH;
	}

	/**
	 * @return the tBETR
	 */
	public BigDecimal getTBETR() {
		return TBETR;
	}

	/**
	 * @param tBETR
	 *            the tBETR to set
	 */
	public void setTBETR(BigDecimal tBETR) {
		TBETR = tBETR;
	}

	/**
	 * @return the bVRKO
	 */
	public String getBVRKO() {
		return BVRKO;
	}

	/**
	 * @param bVRKO
	 *            the bVRKO to set
	 */
	public void setBVRKO(String bVRKO) {
		BVRKO = bVRKO;
	}

	/**
	 * @return the bUKRS
	 */
	public String getBUKRS() {
		return BUKRS;
	}

	/**
	 * @param bUKRS
	 *            the bUKRS to set
	 */
	public void setBUKRS(String bUKRS) {
		BUKRS = bUKRS;
	}

	/**
	 * @return the gSBER
	 */
	public String getGSBER() {
		return GSBER;
	}

	/**
	 * @param gSBER
	 *            the gSBER to set
	 */
	public void setGSBER(String gSBER) {
		GSBER = gSBER;
	}

	/**
	 * @return the bLART
	 */
	public String getBLART() {
		return BLART;
	}

	/**
	 * @param bLART
	 *            the bLART to set
	 */
	public void setBLART(String bLART) {
		BLART = bLART;
	}

	/**
	 * @return the wAERS
	 */
	public String getWAERS() {
		return WAERS;
	}

	/**
	 * @param wAERS
	 *            the wAERS to set
	 */
	public void setWAERS(String wAERS) {
		WAERS = wAERS;
	}

	/**
	 * @return the kURSF
	 */
	public String getKURSF() {
		return KURSF;
	}

	/**
	 * @param kURSF
	 *            the kURSF to set
	 */
	public void setKURSF(String kURSF) {
		KURSF = kURSF;
	}

	/**
	 * @return the bUDAT
	 */
	public Date getBUDAT() {
		return BUDAT;
	}

	/**
	 * @param bUDAT
	 *            the bUDAT to set
	 */
	public void setBUDAT(Date bUDAT) {
		BUDAT = bUDAT;
	}

	/**
	 * @return the bLDAT
	 */
	public Date getBLDAT() {
		return BLDAT;
	}

	/**
	 * @param bLDAT
	 *            the bLDAT to set
	 */
	public void setBLDAT(Date bLDAT) {
		BLDAT = bLDAT;
	}

	/**
	 * @return the vALUT
	 */
	public Date getVALUT() {
		return VALUT;
	}

	/**
	 * @param vALUT
	 *            the vALUT to set
	 */
	public void setVALUT(Date vALUT) {
		VALUT = vALUT;
	}

	/**
	 * @return the xEIPH
	 */
	public String getXEIPH() {
		return XEIPH;
	}

	/**
	 * @param xEIPH
	 *            the xEIPH to set
	 */
	public void setXEIPH(String xEIPH) {
		XEIPH = xEIPH;
	}

	/**
	 * @return the aUGRD
	 */
	public String getAUGRD() {
		return AUGRD;
	}

	/**
	 * @param aUGRD
	 *            the aUGRD to set
	 */
	public void setAUGRD(String aUGRD) {
		AUGRD = aUGRD;
	}

	/**
	 * @return the xAKON
	 */
	public String getXAKON() {
		return XAKON;
	}

	/**
	 * @param xAKON
	 *            the xAKON to set
	 */
	public void setXAKON(String xAKON) {
		XAKON = xAKON;
	}

	/**
	 * @return the xKLAE
	 */
	public String getXKLAE() {
		return XKLAE;
	}

	/**
	 * @param xKLAE
	 *            the xKLAE to set
	 */
	public void setXKLAE(String xKLAE) {
		XKLAE = xKLAE;
	}

	/**
	 * @return the kLAEH
	 */
	public String getKLAEH() {
		return KLAEH;
	}

	/**
	 * @param kLAEH
	 *            the kLAEH to set
	 */
	public void setKLAEH(String kLAEH) {
		KLAEH = kLAEH;
	}

	/**
	 * @return the tXTVW
	 */
	public String getTXTVW() {
		return TXTVW;
	}

	/**
	 * @param tXTVW
	 *            the tXTVW to set
	 */
	public void setTXTVW(String tXTVW) {
		TXTVW = tXTVW;
	}

	/**
	 * @return the cHCKN
	 */
	public String getCHCKN() {
		return CHCKN;
	}

	/**
	 * @param cHCKN
	 *            the cHCKN to set
	 */
	public void setCHCKN(String cHCKN) {
		CHCKN = cHCKN;
	}

	/**
	 * @return the bANKS
	 */
	public String getBANKS() {
		return BANKS;
	}

	/**
	 * @param bANKS
	 *            the bANKS to set
	 */
	public void setBANKS(String bANKS) {
		BANKS = bANKS;
	}

	/**
	 * @return the bANKL
	 */
	public String getBANKL() {
		return BANKL;
	}

	/**
	 * @param bANKL
	 *            the bANKL to set
	 */
	public void setBANKL(String bANKL) {
		BANKL = bANKL;
	}

	/**
	 * @return the bANKN
	 */
	public String getBANKN() {
		return BANKN;
	}

	/**
	 * @param bANKN
	 *            the bANKN to set
	 */
	public void setBANKN(String bANKN) {
		BANKN = bANKN;
	}

	/**
	 * @return the bKONT
	 */
	public String getBKONT() {
		return BKONT;
	}

	/**
	 * @param bKONT
	 *            the bKONT to set
	 */
	public void setBKONT(String bKONT) {
		BKONT = bKONT;
	}

	/**
	 * @return the kOINH
	 */
	public String getKOINH() {
		return KOINH;
	}

	/**
	 * @param kOINH
	 *            the kOINH to set
	 */
	public void setKOINH(String kOINH) {
		KOINH = kOINH;
	}

	/**
	 * @return the xPGRO
	 */
	public String getXPGRO() {
		return XPGRO;
	}

	/**
	 * @param xPGRO
	 *            the xPGRO to set
	 */
	public void setXPGRO(String xPGRO) {
		XPGRO = xPGRO;
	}

	/**
	 * @return the xDAUB
	 */
	public String getXDAUB() {
		return XDAUB;
	}

	/**
	 * @param xDAUB
	 *            the xDAUB to set
	 */
	public void setXDAUB(String xDAUB) {
		XDAUB = xDAUB;
	}

	/**
	 * @return the iNFOF
	 */
	public String getINFOF() {
		return INFOF;
	}

	/**
	 * @param iNFOF
	 *            the iNFOF to set
	 */
	public void setINFOF(String iNFOF) {
		INFOF = iNFOF;
	}

	/**
	 * @return the kUKON
	 */
	public String getKUKON() {
		return KUKON;
	}

	/**
	 * @param kUKON
	 *            the kUKON to set
	 */
	public void setKUKON(String kUKON) {
		KUKON = kUKON;
	}

	/**
	 * @return the bKREF
	 */
	public String getBKREF() {
		return BKREF;
	}

	/**
	 * @param bKREF
	 *            the bKREF to set
	 */
	public void setBKREF(String bKREF) {
		BKREF = bKREF;
	}

	/**
	 * @return the cCINS
	 */
	public String getCCINS() {
		return CCINS;
	}

	/**
	 * @param cCINS
	 *            the cCINS to set
	 */
	public void setCCINS(String cCINS) {
		CCINS = cCINS;
	}

	/**
	 * @return the cCNUM
	 */
	public String getCCNUM() {
		return CCNUM;
	}

	/**
	 * @param cCNUM
	 *            the cCNUM to set
	 */
	public void setCCNUM(String cCNUM) {
		CCNUM = cCNUM;
	}

	/**
	 * @return the dATAB
	 */
	public Date getDATAB() {
		return DATAB;
	}

	/**
	 * @param dATAB
	 *            the dATAB to set
	 */
	public void setDATAB(Date dATAB) {
		DATAB = dATAB;
	}

	/**
	 * @return the dATBI
	 */
	public Date getDATBI() {
		return DATBI;
	}

	/**
	 * @param dATBI
	 *            the dATBI to set
	 */
	public void setDATBI(Date dATBI) {
		DATBI = dATBI;
	}

	/**
	 * @return the aUNUM
	 */
	public String getAUNUM() {
		return AUNUM;
	}

	/**
	 * @param aUNUM
	 *            the aUNUM to set
	 */
	public void setAUNUM(String aUNUM) {
		AUNUM = aUNUM;
	}

	/**
	 * @return the aUDAT
	 */
	public Date getAUDAT() {
		return AUDAT;
	}

	/**
	 * @param aUDAT
	 *            the aUDAT to set
	 */
	public void setAUDAT(Date aUDAT) {
		AUDAT = aUDAT;
	}

	/**
	 * @return the aUTIM
	 */
	public String getAUTIM() {
		return AUTIM;
	}

	/**
	 * @param aUTIM
	 *            the aUTIM to set
	 */
	public void setAUTIM(String aUTIM) {
		AUTIM = aUTIM;
	}

	/**
	 * @return the pRCTR
	 */
	public String getPRCTR() {
		return PRCTR;
	}

	/**
	 * @param pRCTR
	 *            the pRCTR to set
	 */
	public void setPRCTR(String pRCTR) {
		PRCTR = pRCTR;
	}

	/**
	 * @return the bEGRU
	 */
	public String getBEGRU() {
		return BEGRU;
	}

	/**
	 * @param bEGRU
	 *            the bEGRU to set
	 */
	public void setBEGRU(String bEGRU) {
		BEGRU = bEGRU;
	}

	/**
	 * @return the mERCH
	 */
	public String getMERCH() {
		return MERCH;
	}

	/**
	 * @param mERCH
	 *            the mERCH to set
	 */
	public void setMERCH(String mERCH) {
		MERCH = mERCH;
	}

	/**
	 * @return the iBAN
	 */
	public String getIBAN() {
		return IBAN;
	}

	/**
	 * @param iBAN
	 *            the iBAN to set
	 */
	public void setIBAN(String iBAN) {
		IBAN = iBAN;
	}

	/**
	 * @return the sWIFT
	 */
	public String getSWIFT() {
		return SWIFT;
	}

	/**
	 * @param sWIFT
	 *            the sWIFT to set
	 */
	public void setSWIFT(String sWIFT) {
		SWIFT = sWIFT;
	}

	/**
	 * @return the eSNUM
	 */
	public String getESNUM() {
		return ESNUM;
	}

	/**
	 * @param eSNUM
	 *            the eSNUM to set
	 */
	public void setESNUM(String eSNUM) {
		ESNUM = eSNUM;
	}

	/**
	 * @return the lNKID
	 */
	public String getLNKID() {
		return LNKID;
	}

	/**
	 * @param lNKID
	 *            the lNKID to set
	 */
	public void setLNKID(String lNKID) {
		LNKID = lNKID;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		return "BFKKZP [STYPE=" + STYPE + ", TBNAM=" + TBNAM + ", SELT1=" + SELT1 + ", SELT2=" + SELT2 + ", SELT3=" + SELT3 + ", SELW1=" + SELW1 + ", SELW2=" + SELW2 + ", SELW3=" + SELW3 + ", BETRZ="
				+ BETRZ + ", BETRH=" + BETRH + ", TBETR=" + TBETR + ", BVRKO=" + BVRKO + ", BUKRS=" + BUKRS + ", GSBER=" + GSBER + ", BLART=" + BLART + ", WAERS=" + WAERS + ", KURSF=" + KURSF
				+ ", BUDAT=" + BUDAT + ", BLDAT=" + BLDAT + ", VALUT=" + VALUT + ", XEIPH=" + XEIPH + ", AUGRD=" + AUGRD + ", XAKON=" + XAKON + ", XKLAE=" + XKLAE + ", KLAEH=" + KLAEH + ", TXTVW="
				+ TXTVW + ", CHCKN=" + CHCKN + ", BANKS=" + BANKS + ", BANKL=" + BANKL + ", BANKN=" + BANKN + ", BKONT=" + BKONT + ", KOINH=" + KOINH + ", XPGRO=" + XPGRO + ", XDAUB=" + XDAUB
				+ ", INFOF=" + INFOF + ", KUKON=" + KUKON + ", BKREF=" + BKREF + ", CCINS=" + CCINS + ", CCNUM=" + CCNUM + ", DATAB=" + DATAB + ", DATBI=" + DATBI + ", AUNUM=" + AUNUM + ", AUDAT="
				+ AUDAT + ", AUTIM=" + AUTIM + ", PRCTR=" + PRCTR + ", BEGRU=" + BEGRU + ", MERCH=" + MERCH + ", IBAN=" + IBAN + ", SWIFT=" + SWIFT + ", ESNUM=" + ESNUM + ", LNKID=" + LNKID + "]";
	}

}
