package com.etr407.erp.services.esb.subscriber.payment.opacfileconverter;

import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenRootGroup;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.exception.DuplicateFileException;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.exception.ValidationException;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Checks for duplicate files based on input fileName. Throws
 * a DuplicateFileException when a duplicate file is found, and
 * delegates the responsibility of notifying users and moving the
 * file to the calling camel route.
 */
public class DuplicateFileProcessor implements Processor {

	private static final Logger LOGGER = LoggerFactory.getLogger(DuplicateFileProcessor.class);
	private final String doneDir;
	private final int fileAge;
	public static final String INVALID_DONE_DIR_MSG = "Done directory location doesn't exist, location=%s";
	private static final String DUPLICATE_FILE_MSG = "Duplicate files found: %s";

	/**
	 * @param doneDir the directory to check for existing payment files.
	 */
	public DuplicateFileProcessor(String doneDir, int fileAge) {
		this.doneDir = doneDir;
		this.fileAge = fileAge;
	}

	/**
	 * Expected vendor filename: opac_request_yyyyMMdd.txt
	 * Expected archive filename: opac_request_yyyyMMdd.txt-yyyyMMddHHmmssSSS
	 * Sequence number field: EigenFileHeader.efSeqNumber
	 * Sets exchange property fileNameWithSeqNum=opac_request_yyyyMMdd.nnn.txt
	 * @param exchange
	 * @throws Exception
	 */
	@Override
	public void process(Exchange exchange) throws ValidationException, DuplicateFileException {
		// ensure archive directory exists
		if (!new File(this.doneDir).exists()) {
			String formattedMessage = String.format(INVALID_DONE_DIR_MSG, this.doneDir);
			throw new ValidationException(formattedMessage);
		}

		// extract data from exchange
		String origFileName = exchange.getProperty("origFileName", String.class);
		List<EigenRootGroup> body = (List<EigenRootGroup>) exchange.getIn().getBody();
		EigenRootGroup eigenRootGroup = body.get(0);

		long fileSeqNum = eigenRootGroup.getFileHeader().getEfSeqNumber() % 1000;
		String fileNameWithSeqNum = String.format("%s.%03d.txt", getFileNameWithoutExt(origFileName), fileSeqNum);
		exchange.setProperty("fileNameWithSeqNum", fileNameWithSeqNum);

		// check for duplicate file names
		LOGGER.info("Checking for duplicates against: fileName={}; fileSeqNum={}", origFileName, fileSeqNum);
		fileNameCheck(origFileName, getSortedFilesPathList());
	}

	/**
	 * Method to traverse and compare against archive directory
	 * assumes all filenames are in the format opac_request_yyyyMMdd.txt
	 * @param inputFileName Input file name without extension
	 * @throws Exception
	 */
	private void fileNameCheck(String inputFileName, List<Path> pathList) throws DuplicateFileException {

		LOGGER.info("DuplicateFileProcessor.fileNameCheck method start");

		for (int i = 0; i <= fileAge && i < pathList.size(); i++) {
			Path path = pathList.get(i);
			String archivedFileName = path.getFileName().toString();
			String archivedFormattedFileName = getArchiveFileName(archivedFileName);

			LOGGER.info("Found file: fileName={}", archivedFormattedFileName);
			if (inputFileName.equals(archivedFormattedFileName)) {
				String formattedMessage = String.format(
						DUPLICATE_FILE_MSG, inputFileName, archivedFormattedFileName);
				throw new DuplicateFileException(formattedMessage);
			}
		}

		LOGGER.info("DuplicateFileProcessor.fileNameCheck method end");
	}

	/**
	 * Utility method to sort the archive directory files
	 * such that the last modified file appear first
	 *
	 * @throws IOException
	 * @throws ValidationException
	 */
	private List<Path> getSortedFilesPathList() throws ValidationException {
		LOGGER.info("DuplicateFileProcessor.getSortedFilesPathList method start");

		List<Path> archivedFiles = new ArrayList<>();

		Path pathToFetch = Paths.get(this.doneDir);

		try (DirectoryStream<Path> stream = Files.newDirectoryStream(pathToFetch)) {
			for (Path p : stream) {
				archivedFiles.add(p);
			}
		} catch (IOException e) {
			LOGGER.error(e.getMessage());
			throw new ValidationException("Unable to read files from the location: " + pathToFetch);
		}
		//sort the list in descending order so the last modified files appear first

		Collections.sort(archivedFiles, (path1, path2) -> {
			try {
				return Files.getLastModifiedTime(path2).compareTo(Files.getLastModifiedTime(path1));
			} catch (IOException e) {
				return 0;
			}
		});

		LOGGER.info("DuplicateFileProcessor.getSortedFilesPathList method end");
		return archivedFiles;
	}

	private static String getFileNameWithoutExt(String fileName) {
		return getFileName(fileName, ".");
	}

	private static String getArchiveFileName(String fileName) {
		return getFileName(fileName, "-");
	}

	/**
	 * Utility method to separate file name from timestamp
	 *
	 * @param fileName Raw input file name with Extension
	 * @param splitStr Delimiter to split suffix on
	 */
	private static String getFileName(String fileName, String splitStr) {
		LOGGER.info("DuplicateFileProcessor.getFileName method start");

		LOGGER.info("Get the file name for input: fileName={}", fileName);
		int splitWordIndexPosition = fileName.lastIndexOf(splitStr);
		String formattedFileName;
		if (splitWordIndexPosition != -1) {
			formattedFileName = fileName.substring(0, splitWordIndexPosition);
		} else {
			formattedFileName = fileName;
		}
		LOGGER.info("File name after removing the extension: fileName={}", formattedFileName);
		LOGGER.info("DuplicateFileProcessor.getFileName method end");
		return formattedFileName;
	}
}
