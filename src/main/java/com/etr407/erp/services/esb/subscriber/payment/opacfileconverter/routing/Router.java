package com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.routing;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenBatch;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenBatchDetail;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenBatchHeader;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenBatchTrailer;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenFileHeader;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenFileTrailer;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenRootGroup;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.exception.ValidationException;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.utils.Base64EchoData;

public class Router implements Processor {

	private static final Logger LOGGER = LoggerFactory.getLogger(Router.class);

	public Router() {
	}

	public void process(Exchange ex) throws SQLException, ValidationException {
		LOGGER.info("Routing started");

		@SuppressWarnings("unchecked")
		List<EigenRootGroup> body = (List<EigenRootGroup>) ex.getIn().getBody();
		if (body == null || body.size() < 1) {
			throw new ValidationException("Cannot route empty file.");
		}

		EigenRootGroup srcRootGroup = body.get(0);
		List<EigenBatchDetail> details = srcRootGroup.getBatches().get(0).getBatchDetails();

		
		// split payment types
		Map<String, List<EigenBatchDetail>> distributions = distributeDetailRecords(details);

		// map payment type/destination name to exchange property names
		@SuppressWarnings("serial")
		Map<String, String> destPropMap = new HashMap<String, String>() {
			{
				put("AX", "EigenRootGroupForSapAmex");
				put("MC", "EigenRootGroupForSapMastercard");
				put("VI", "EigenRootGroupForSapVisa");
				put("DECLINED", "EigenRootGroupForSapDeclined");
			}
		};

		// create EigenRootGroup instances for each payment type/destination
		for (String paymentType : distributions.keySet()) {
			EigenRootGroup rootGroup = generateStubRootGroup(srcRootGroup, paymentType);
			rootGroup.getBatches().get(0).setBatchDetails(distributions.get(paymentType));
			updateTrailer(rootGroup);
			// set exchange property if and only if there is at least one detail record for
			// the payment type
			if (rootGroup.getFileTrailer().getTotalDetailLinesInFile() > 0) {
				List<EigenRootGroup> rootGroupContainer = new ArrayList<>();
				rootGroupContainer.add(rootGroup);
				ex.setProperty(destPropMap.get(paymentType), rootGroupContainer);
			} else {
				LOGGER.warn("no payments found for payment type {}", paymentType);
			}
		}
		LOGGER.info("Routing complete");
	}

	/**
	 * Update file and batch trailer in an EigenRootGroup instance. Assuming we have
	 * only one batch
	 *
	 * @param rootGroup generated EigenRootGroup instance
	 */
	private void updateTrailer(EigenRootGroup rootGroup) {
		int totalDetailLines = 0;
		BigDecimal totalFileAmount = BigDecimal.ZERO;

		// update batch trailer
		EigenBatch batch = rootGroup.getBatches().get(0);
		EigenBatchTrailer batchTrailer = batch.getBatchTrailer();
		EigenFileTrailer fileTrailer = rootGroup.getFileTrailer();

		BigDecimal totalBatchAmount = batch.getBatchDetails().stream().map(EigenBatchDetail::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

		batchTrailer.setTotalDetailLinesInBatch(batch.getBatchDetails().size());
		batchTrailer.setTotalBatchAmount(totalBatchAmount);

		totalDetailLines = batch.getBatchDetails().size();
		totalFileAmount = totalBatchAmount;

		// update file trailer
		fileTrailer.setNumberOfBatches(1);
		fileTrailer.setTotalDetailLinesInFile(totalDetailLines);
		fileTrailer.setTotalFileAmount(totalFileAmount);
	}

	/**
	 * Given a reference EigenRootGroup, generates a stub EigenRootGroup with a
	 * single batch.
	 *
	 * Note that the single-batch detail list is unset since it's expected that it
	 * will be set outside this method.
	 *
	 * @param reference   reference EigenRootGroup
	 * @param paymentType
	 * @return a stub EigenRootGroup instance
	 */
	private EigenRootGroup generateStubRootGroup(EigenRootGroup reference, String paymentType) {

		Base64EchoData base64EchoData = new Base64EchoData(reference.getBatches().get(0).getBatchHeader().getEchoDataBatch());

		LOGGER.info("Router - decoded base64 echo data: '{}' ({}={}; {}={})", base64EchoData.toString(), "totalRecords", base64EchoData.getTotalRecords(), "totalAmount", base64EchoData.getTotalAmount());

		EigenRootGroup rootGroup = new EigenRootGroup();

		EigenFileHeader fileHeader = reference.getFileHeader().copy();
		EigenFileTrailer fileTrailer = reference.getFileTrailer().copy();

		// create single batch
		EigenBatch batch = new EigenBatch();
		EigenBatchHeader batchHeader = reference.getBatches().get(0).getBatchHeader().copy();
		EigenBatchTrailer batchTrailer = reference.getBatches().get(0).getBatchTrailer().copy();

		// Set file header record
		rootGroup.setFileHeader(fileHeader);

		batch.setBatchHeader(batchHeader);
		batch.setBatchTrailer(batchTrailer);
		rootGroup.setBatches(new ArrayList<>());
		rootGroup.getBatches().add(batch);

		rootGroup.setFileTrailer(fileTrailer);

		return rootGroup;
	}

	/**
	 * Distributes detail records based on customer destination (i.e. PS or SAP),
	 * and furthermore routes Amex, Mastercard, Visa, and declined payments for SAP
	 * (+ unknown) customers.
	 *
	 * Returns a Map of EigenBatchDetail lists containing the following keys for
	 * each destination:
	 *
	 * PS: Payments destined for PeopleSoft AX: American Express payments destined
	 * for SAP MC: Mastercard payments destined for SAP VI: Visa payments destined
	 * for SAP DECLINED: Declined payments destined for SAP
	 *
	 * @param details      list of EigenBatchDetail records
	 * @param sapCustomers set of SAP customer IDs
	 * @param psCustomers  set of PeopleSoft customer IDs
	 * @return a mapping of payment types mapped to lists of detail records for the
	 *         respective payment type
	 * @throws ValidationException if an invalid payment type is found
	 */
	private Map<String, List<EigenBatchDetail>> distributeDetailRecords(List<EigenBatchDetail> details) throws ValidationException {
		@SuppressWarnings("serial")
		Map<String, List<EigenBatchDetail>> distribution = new HashMap<String, List<EigenBatchDetail>>() {
			{
				put("AX", new ArrayList<>());
				put("MC", new ArrayList<>());
				put("VI", new ArrayList<>());
				put("DECLINED", new ArrayList<>());
			}
		};
		for (EigenBatchDetail detail : details) {
			String paymentType = detail.getExtOpId().trim().substring(0, 2).toUpperCase();
			boolean approved = detail.getErrorDesc().toUpperCase().contains("APPROVED");
			paymentType = approved ? paymentType : "DECLINED";
			LOGGER.info("Payment type is {} and approval state is {}", paymentType, approved);
			if (distribution.containsKey(paymentType)) {
				distribution.get(paymentType).add(detail);
			} else {
				throw new ValidationException(String.format("unknown payment type: %s", paymentType));
			}
		}
		return distribution;
	}
}
