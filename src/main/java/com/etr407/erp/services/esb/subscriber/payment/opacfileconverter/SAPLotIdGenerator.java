package com.etr407.erp.services.esb.subscriber.payment.opacfileconverter;

import java.io.IOException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenBatch;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenRootGroup;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.exception.ValidationException;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.utils.SAPUtils;

/**
 * Camel processor class used to determine the next SAP lot ID.
 */
public class SAPLotIdGenerator implements Processor {

	private static final DateFormat processDateFormat = new SimpleDateFormat("yyyyMMdd");
	private static final DateFormat paymentLotDateFormat = new SimpleDateFormat("yyMMdd");
	private static final Logger LOGGER = LoggerFactory.getLogger(SAPLotIdGenerator.class);
	private static final String VENDOR_LOT_ID_AFFIX = "OA";

	public void process(Exchange ex) throws IOException, ParseException, ValidationException {
		LOGGER.info("SAPLotIdGenerator.process method started");
		// generate SAP lot id
		int sapLotIdSuffixNum = (int) ex.getProperty("nextSeqNum");
		int sapLotIdSuffixNumDeclined = sapLotIdSuffixNum % 10;
		int sapLotIdSuffixNumApproved = sapLotIdSuffixNum % 100;
		Date processDate = processDateFormat.parse((String) ex.getProperty("processDate"));
		if(processDate != null){
			String paymentType = getPaymentType(ex);
			String sapLotIdPrefix;
			String sapLotId;
			if ("PRN".equals(paymentType)){
				sapLotIdPrefix = paymentType + "_" + paymentLotDateFormat.format(processDate);
				sapLotId = sapLotIdPrefix + "_" + sapLotIdSuffixNumDeclined;
			}else{
				// final lotId format is XXOAyyMMddnn
				sapLotId = String.format(
						"%s%s%s%02d",
						paymentType,
						VENDOR_LOT_ID_AFFIX,
						paymentLotDateFormat.format(processDate),
						sapLotIdSuffixNumApproved);
			}
			LOGGER.info("SAP Lot ID is " + sapLotId);
			ex.getIn().setHeader("sapLotId", sapLotId);
			ex.setProperty("sapLotId", sapLotId);
		} else {
			throw new ValidationException("Invalid process date");
		}
		LOGGER.info("SAPLotIdGenerator.process method ended");
	}

	/**
	 * Retrieves the payment type from input file Possible values are VI,MC,AX.
	 * 
	 * @param exchange {@code Exchange} instance.
	 * @throws ValidationException if there is an error reading the SAP payment
	 *                             type.
	 */
	private String getPaymentType(Exchange exchange) throws ValidationException {
		String paymentType;
		@SuppressWarnings("unchecked")
		List<EigenRootGroup> body = (List<EigenRootGroup>) exchange.getIn().getBody();
		// Body always contains only 1 element
		EigenRootGroup eigenRootGroup = body.get(0);
		List<EigenBatch> batches = eigenRootGroup.getBatches();
		boolean approved = batches.get(0).getBatchDetails().get(0).getErrorDesc().toUpperCase().contains("APPROVED");
		if (!approved) {
			paymentType = "PRN";
		} else {
			paymentType = SAPUtils.getCardType(batches);
		}
		return paymentType;
	}

}
