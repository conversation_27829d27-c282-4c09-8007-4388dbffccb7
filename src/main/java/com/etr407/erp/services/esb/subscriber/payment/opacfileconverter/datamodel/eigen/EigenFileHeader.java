package com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen;

import java.util.Date;

public class EigenFileHeader {

	private String recordType = "F";
	private Date submissionDate;
	private Date processedDate;
	private long ofSeqNumber;
	private long efSeqNumber;
	private String uid;

	public String getRecordType() {
		return recordType;
	}

	public void setRecordType(String recordType) {
		this.recordType = recordType;
	}

	public Date getSubmissionDate() {
		return submissionDate;
	}

	public void setSubmissionDate(Date submissionDate) {
		this.submissionDate = submissionDate;
	}

	public Date getProcessedDate() {
		return processedDate;
	}

	public void setProcessedDate(Date processedDate) {
		this.processedDate = processedDate;
	}

	public long getOfSeqNumber() {
		return ofSeqNumber;
	}

	public void setOfSeqNumber(long ofSeqNumber) {
		this.ofSeqNumber = ofSeqNumber;
	}

	public long getEfSeqNumber() {
		return efSeqNumber;
	}

	public void setEfSeqNumber(long efSeqNumber) {
		this.efSeqNumber = efSeqNumber;
	}

	public String getUid() {
		return uid;
	}

	public void setUid(String uid) {
		this.uid = uid;
	}

	@Override
	public String toString() {
		return "EigenFileHeader{" +
				"recordType=" + recordType +
				", submissionDate=" + submissionDate +
				", processedDate=" + processedDate +
				", ofSeqNumber=" + ofSeqNumber +
				", efSeqNumber=" + efSeqNumber +
				", uid='" + uid + '\'' +
				'}';
	}

	public EigenFileHeader copy() {
		EigenFileHeader eigenFileHeader = new EigenFileHeader();
		eigenFileHeader.setRecordType(this.getRecordType());
		eigenFileHeader.setSubmissionDate(this.getSubmissionDate());
		eigenFileHeader.setProcessedDate(this.getProcessedDate());
		eigenFileHeader.setOfSeqNumber(this.getOfSeqNumber());
		eigenFileHeader.setEfSeqNumber(this.getEfSeqNumber());
		eigenFileHeader.setUid(this.getUid());
		return eigenFileHeader;
	}
}
