package com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.sap.declined;

import java.math.BigDecimal;
import java.util.Date;

public class BFKKRP {

	String STYPE = "2";
	String TBNAM;
	String SELT1 = "B";
	String SELW1;
	BigDecimal BETRR;
	BigDecimal BETRH;
	BigDecimal BTRB1;
	BigDecimal BTRB2;
	BigDecimal BTRV1;
	BigDecimal BTRV2;
	BigDecimal STBB1;
	BigDecimal STBB2;
	BigDecimal STBV1;
	BigDecimal STBV2;
	String SKZB1;
	String SKZB2;
	String SKZV1;
	String SKZV2;
	String RLSKO;
	String BUKRS = "407";
	String GSBER;
	String BLART = "RT";
	String WAERS = "CAD";
	String KURSF;
	Date BUDAT;
	Date BLDAT;
	Date VALUT;
	String XEIPH;
	String RLGRD;
	String RLHBK;
	String TXTVW;
	String BANKL;
	String BANKK;
	String BANKN;
	String IBAN;
	String CHECF;
	String XACCEPTCHARGES;
	String HBKID;
	String HKTID;
	String RLMOD;
	String XERWR;
	String PRCTR;
	String KUKEY;
	String ESNUM;
	String SWIFT;

	public String getSTYPE() {
		return STYPE;
	}

	public void setSTYPE(String sTYPE) {
		STYPE = sTYPE;
	}

	public String getTBNAM() {
		return TBNAM;
	}

	public void setTBNAM(String tBNAM) {
		TBNAM = tBNAM;
	}

	public String getSELT1() {
		return SELT1;
	}

	public void setSELT1(String sELT1) {
		SELT1 = sELT1;
	}

	public String getSELW1() {
		return SELW1;
	}

	public void setSELW1(String sELW1) {
		SELW1 = sELW1;
	}

	public BigDecimal getBETRR() {
		return BETRR;
	}

	public void setBETRR(BigDecimal bETRR) {
		BETRR = bETRR;
	}

	public BigDecimal getBETRH() {
		return BETRH;
	}

	public void setBETRH(BigDecimal bETRH) {
		BETRH = bETRH;
	}

	public BigDecimal getBTRB1() {
		return BTRB1;
	}

	public void setBTRB1(BigDecimal bTRB1) {
		BTRB1 = bTRB1;
	}

	public BigDecimal getBTRB2() {
		return BTRB2;
	}

	public void setBTRB2(BigDecimal bTRB2) {
		BTRB2 = bTRB2;
	}

	public BigDecimal getBTRV1() {
		return BTRV1;
	}

	public void setBTRV1(BigDecimal bTRV1) {
		BTRV1 = bTRV1;
	}

	public BigDecimal getBTRV2() {
		return BTRV2;
	}

	public void setBTRV2(BigDecimal bTRV2) {
		BTRV2 = bTRV2;
	}

	public BigDecimal getSTBB1() {
		return STBB1;
	}

	public void setSTBB1(BigDecimal sTBB1) {
		STBB1 = sTBB1;
	}

	public BigDecimal getSTBB2() {
		return STBB2;
	}

	public void setSTBB2(BigDecimal sTBB2) {
		STBB2 = sTBB2;
	}

	public BigDecimal getSTBV1() {
		return STBV1;
	}

	public void setSTBV1(BigDecimal sTBV1) {
		STBV1 = sTBV1;
	}

	public BigDecimal getSTBV2() {
		return STBV2;
	}

	public void setSTBV2(BigDecimal sTBV2) {
		STBV2 = sTBV2;
	}

	public String getSKZB1() {
		return SKZB1;
	}

	public void setSKZB1(String sKZB1) {
		SKZB1 = sKZB1;
	}

	public String getSKZB2() {
		return SKZB2;
	}

	public void setSKZB2(String sKZB2) {
		SKZB2 = sKZB2;
	}

	public String getSKZV1() {
		return SKZV1;
	}

	public void setSKZV1(String sKZV1) {
		SKZV1 = sKZV1;
	}

	public String getSKZV2() {
		return SKZV2;
	}

	public void setSKZV2(String sKZV2) {
		SKZV2 = sKZV2;
	}

	public String getRLSKO() {
		return RLSKO;
	}

	public void setRLSKO(String rLSKO) {
		RLSKO = rLSKO;
	}

	public String getBUKRS() {
		return BUKRS;
	}

	public void setBUKRS(String bUKRS) {
		BUKRS = bUKRS;
	}

	public String getGSBER() {
		return GSBER;
	}

	public void setGSBER(String gSBER) {
		GSBER = gSBER;
	}

	public String getBLART() {
		return BLART;
	}

	public void setBLART(String bLART) {
		BLART = bLART;
	}

	public String getWAERS() {
		return WAERS;
	}

	public void setWAERS(String wAERS) {
		WAERS = wAERS;
	}

	public String getKURSF() {
		return KURSF;
	}

	public void setKURSF(String kURSF) {
		KURSF = kURSF;
	}

	public Date getBUDAT() {
		return BUDAT;
	}

	public void setBUDAT(Date bUDAT) {
		BUDAT = bUDAT;
	}

	public Date getBLDAT() {
		return BLDAT;
	}

	public void setBLDAT(Date bLDAT) {
		BLDAT = bLDAT;
	}

	public Date getVALUT() {
		return VALUT;
	}

	public void setVALUT(Date vALUT) {
		VALUT = vALUT;
	}

	public String getXEIPH() {
		return XEIPH;
	}

	public void setXEIPH(String xEIPH) {
		XEIPH = xEIPH;
	}

	public String getRLGRD() {
		return RLGRD;
	}

	public void setRLGRD(String rLGRD) {
		RLGRD = rLGRD;
	}

	public String getRLHBK() {
		return RLHBK;
	}

	public void setRLHBK(String rLHBK) {
		RLHBK = rLHBK;
	}

	public String getTXTVW() {
		return TXTVW;
	}

	public void setTXTVW(String tXTVW) {
		TXTVW = tXTVW;
	}

	public String getBANKL() {
		return BANKL;
	}

	public void setBANKL(String bANKL) {
		BANKL = bANKL;
	}

	public String getBANKK() {
		return BANKK;
	}

	public void setBANKK(String bANKK) {
		BANKK = bANKK;
	}

	public String getBANKN() {
		return BANKN;
	}

	public void setBANKN(String bANKN) {
		BANKN = bANKN;
	}

	public String getIBAN() {
		return IBAN;
	}

	public void setIBAN(String iBAN) {
		IBAN = iBAN;
	}

	public String getCHECF() {
		return CHECF;
	}

	public void setCHECF(String cHECF) {
		CHECF = cHECF;
	}

	public String getXACCEPTCHARGES() {
		return XACCEPTCHARGES;
	}

	public void setXACCEPTCHARGES(String xACCEPTCHARGES) {
		XACCEPTCHARGES = xACCEPTCHARGES;
	}

	public String getHBKID() {
		return HBKID;
	}

	public void setHBKID(String hBKID) {
		HBKID = hBKID;
	}

	public String getHKTID() {
		return HKTID;
	}

	public void setHKTID(String hKTID) {
		HKTID = hKTID;
	}

	public String getRLMOD() {
		return RLMOD;
	}

	public void setRLMOD(String rLMOD) {
		RLMOD = rLMOD;
	}

	public String getXERWR() {
		return XERWR;
	}

	public void setXERWR(String xERWR) {
		XERWR = xERWR;
	}

	public String getPRCTR() {
		return PRCTR;
	}

	public void setPRCTR(String pRCTR) {
		PRCTR = pRCTR;
	}

	public String getKUKEY() {
		return KUKEY;
	}

	public void setKUKEY(String kUKEY) {
		KUKEY = kUKEY;
	}

	public String getESNUM() {
		return ESNUM;
	}

	public void setESNUM(String eSNUM) {
		ESNUM = eSNUM;
	}

	public String getSWIFT() {
		return SWIFT;
	}

	public void setSWIFT(String sWIFT) {
		SWIFT = sWIFT;
	}

	@Override
	public String toString() {
		return "BFKKRP [STYPE=" + STYPE + ", TBNAM=" + TBNAM + ", SELT1=" + SELT1 + ", SELW1=" + SELW1 + ", BETRR=" + BETRR + ", BETRH=" + BETRH + ", BTRB1=" + BTRB1 + ", BTRB2=" + BTRB2 + ", BTRV1=" + BTRV1 + ", BTRV2=" + BTRV2
				+ ", STBB1=" + STBB1 + ", STBB2=" + STBB2 + ", STBV1=" + STBV1 + ", STBV2=" + STBV2 + ", SKZB1=" + SKZB1 + ", SKZB2=" + SKZB2 + ", SKZV1=" + SKZV1 + ", SKZV2=" + SKZV2 + ", RLSKO=" + RLSKO + ", BUKRS=" + BUKRS + ", GSBER="
				+ GSBER + ", BLART=" + BLART + ", WAERS=" + WAERS + ", KURSF=" + KURSF + ", BUDAT=" + BUDAT + ", BLDAT=" + BLDAT + ", VALUT=" + VALUT + ", XEIPH=" + XEIPH + ", RLGRD=" + RLGRD + ", RLHBK=" + RLHBK + ", TXTVW=" + TXTVW
				+ ", BANKL=" + BANKL + ", BANKK=" + BANKK + ", BANKN=" + BANKN + ", IBAN=" + IBAN + ", CHECF=" + CHECF + ", XACCEPTCHARGES=" + XACCEPTCHARGES + ", HBKID=" + HBKID + ", HKTID=" + HKTID + ", RLMOD=" + RLMOD + ", XERWR="
				+ XERWR + ", PRCTR=" + PRCTR + ", KUKEY=" + KUKEY + ", ESNUM=" + ESNUM + ", SWIFT=" + SWIFT + "]";
	}

}
