package com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen;

import java.math.BigDecimal;

public class EigenBatchTrailer {

	private String recordType = "T";
	private long obSeqNumber;
	private long ebSeqNumber;
	private long totalDetailLinesInBatch;
	private BigDecimal totalBatchAmount;

	public String getRecordType() {
		return recordType;
	}

	public void setRecordType(String recordType) {
		this.recordType = recordType;
	}

	public long getObSeqNumber() {
		return obSeqNumber;
	}

	public void setObSeqNumber(long obSeqNumber) {
		this.obSeqNumber = obSeqNumber;
	}

	public long getEbSeqNumber() {
		return ebSeqNumber;
	}

	public void setEbSeqNumber(long ebSeqNumber) {
		this.ebSeqNumber = ebSeqNumber;
	}

	public long getTotalDetailLinesInBatch() {
		return totalDetailLinesInBatch;
	}

	public void setTotalDetailLinesInBatch(long totalDetailLinesInBatch) {
		this.totalDetailLinesInBatch = totalDetailLinesInBatch;
	}

	public BigDecimal getTotalBatchAmount() {
		return totalBatchAmount;
	}

	public void setTotalBatchAmount(BigDecimal totalBatchAmount) {
		this.totalBatchAmount = totalBatchAmount;
	}

	@Override
	public String toString() {
		return "EigenBatchTrailer{" +
				"recordType=" + recordType +
				", obSeqNumber=" + obSeqNumber +
				", ebSeqNumber=" + ebSeqNumber +
				", totalDetailLinesInBatch=" + totalDetailLinesInBatch +
				", totalBatchAmount=" + totalBatchAmount +
				'}';
	}

	public EigenBatchTrailer copy() {
		EigenBatchTrailer eigenBatchTrailer = new EigenBatchTrailer();
		eigenBatchTrailer.setRecordType(this.getRecordType());
		eigenBatchTrailer.setObSeqNumber(this.getObSeqNumber());
		eigenBatchTrailer.setEbSeqNumber(this.getEbSeqNumber());
		eigenBatchTrailer.setTotalDetailLinesInBatch(this.getTotalDetailLinesInBatch());
		eigenBatchTrailer.setTotalBatchAmount(this.getTotalBatchAmount());
		return eigenBatchTrailer;
	}
}
