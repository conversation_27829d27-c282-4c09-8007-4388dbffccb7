package com.etr407.erp.services.esb.subscriber.payment.opacfileconverter;

import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.exception.ValidationException;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.text.ParseException;

/**
 * Extracts the values from the exchange property to be set into the header,
 * this value will be used to set the error file name in router.done and sapMapper.done folders
 */
public class OPACFailureProcessor implements Processor {

	private static final Logger LOGGER = LoggerFactory.getLogger(OPACFailureProcessor.class);
	private static final String INVALID_FILE_MSG = "Invalid input file: (fileName=%s)";

	@Override
	public void process(Exchange exchange) throws Exception {

		String fileNameWithExt;
		String fileNameWithoutExt;
		String fileExt;

		if((null != exchange.getProperty("origFileName")) && (null != exchange.getProperty("fileNameWithoutExt"))){
			fileNameWithExt = (String) exchange.getProperty("origFileName");
			fileNameWithoutExt = (String) exchange.getProperty("fileNameWithoutExt");
			fileExt = getFileExt(fileNameWithExt);

			if (StringUtils.isNotBlank(fileNameWithoutExt) && StringUtils.isNotBlank(fileExt)) {
				exchange.getIn().setHeader("errorFileHeader", fileNameWithoutExt);
				exchange.getIn().setHeader("fileExt", fileExt);
			}else{
				throw new ValidationException("Missing file name in input file");
			}
		}
	}

	/**
	 * Utility method to get the input file extension
	 *
	 * @param fileName Raw input file name with Extension
	 * @throws IOException
	 * @throws ParseException
	 */
	public static String getFileExt(String fileName) throws ValidationException {

		LOGGER.info("Get the file extension for input: fileExt={}", fileName);
		int splitWordIndexPosition = fileName.lastIndexOf(".");
		String formattedFileExt = null;

		if (splitWordIndexPosition != -1) {
			formattedFileExt = fileName.substring(++splitWordIndexPosition);
		} else {
			String formattedMessage = String.format(INVALID_FILE_MSG, fileName);
			throw new ValidationException(formattedMessage);
		}
		LOGGER.info("File extension: {}", formattedFileExt);
		return formattedFileExt;
	}
}
