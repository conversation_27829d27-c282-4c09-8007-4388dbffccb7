package com.etr407.erp.services.esb.subscriber.payment.opacfileconverter;

import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.context.annotation.ImportResource;

@ImportResource("classpath:/META-INF/spring/camel-context.xml")
public class OPACFileConverterApplication {

	public static void main(String[] args) throws Exception {
		AnnotationConfigApplicationContext context = new AnnotationConfigApplicationContext(OPACFileConverterApplication.class);
	}
}
