package com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen;

import java.util.List;

public class EigenBatch {
	private EigenBatchHeader batchHeader;
	private List<EigenBatchDetail> batchDetails;
	private EigenBatchTrailer batchTrailer;

	public EigenBatchHeader getBatchHeader() {
		return batchHeader;
	}

	public void setBatchHeader(EigenBatchHeader batchHeader) {
		this.batchHeader = batchHeader;
	}

	public List<EigenBatchDetail> getBatchDetails() {
		return batchDetails;
	}

	public void setBatchDetails(List<EigenBatchDetail> batchDetails) {
		this.batchDetails = batchDetails;
	}

	public EigenBatchTrailer getBatchTrailer() {
		return batchTrailer;
	}

	public void setBatchTrailer(EigenBatchTrailer batchTrailer) {
		this.batchTrailer = batchTrailer;
	}

    @Override
    public String toString() {
        return "EigenBatch{" +
                "batchHeader=" + batchHeader +
                ", batchDetails=" + batchDetails +
                ", batchTrailer=" + batchTrailer +
                '}';
    }
}
