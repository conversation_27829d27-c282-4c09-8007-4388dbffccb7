package com.etr407.erp.services.esb.subscriber.payment.opacfileconverter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenBatch;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenBatchDetail;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenBatchTrailer;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenFileTrailer;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.eigen.EigenRootGroup;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.exception.ValidationException;
import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.utils.Base64EchoData;

public class OPACFileBusinessRulesValidator implements Processor {

	private static final Logger LOGGER = LoggerFactory.getLogger(OPACFileBusinessRulesValidator.class);

	/**
	 * Checks that an input file in the exchange body (instance of
	 * {@code List<EigenRootGroup>}) is valid.
	 *
	 * @param ex an {@code Exchange} instance
	 * @throws ValidationException thrown when the file is invalid or empty
	 */
	public void process(Exchange ex) throws ValidationException {
		LOGGER.info("OPAC File Business Rules Validation Started");
		@SuppressWarnings("unchecked")
		List<EigenRootGroup> body = (List<EigenRootGroup>) ex.getIn().getBody();
		if (body == null || body.size() < 1) {
			throw new ValidationException("Cannot validate empty file.");
		}

		EigenRootGroup srcRootGroup = body.get(0);
		List<EigenBatch> batches = srcRootGroup.getBatches();
		List<String> errorList = new ArrayList<>();

		if (batches.size() > 1) {
			throw new ValidationException("More than one batch is not supported!");
		}

		// We are validating the unsplited file from Eigen
		if (ex.getProperties().containsKey("unmarshalledPaymentsFile")) {
			int nextSeqNum = (int) ex.getProperty("nextSeqNum");
			if (srcRootGroup.getFileHeader().getOfSeqNumber() != nextSeqNum) {
				String msg = String.format("Header fileNumber != nextSeqNum (%d != %d)", srcRootGroup.getFileHeader().getOfSeqNumber(), nextSeqNum);
				errorList.add(msg);
			}
			validateEchoData(srcRootGroup, errorList);
		}

		validateBatch(batches.get(0), errorList);
		validateFile(srcRootGroup, errorList);

		if ((boolean) ex.getProperty("validateOnly", false)) {
			ex.getIn().setBody(errorList);
		} else if (errorList.size() > 0) {
			throw new ValidationException(String.join("; ", errorList));
		}
		LOGGER.info("OPAC File Business Rules Validation ended");

	}

	private void validateEchoData(EigenRootGroup srcRootGroup, List<String> errorList) {
		EigenBatch eigenBatch = srcRootGroup.getBatches().get(0);
		EigenFileTrailer fileTrailer = srcRootGroup.getFileTrailer();
		EigenBatchTrailer batchTrailer = srcRootGroup.getBatches().get(0).getBatchTrailer();

		Base64EchoData base64EchoData = new Base64EchoData(eigenBatch.getBatchHeader().getEchoDataBatch());

		LOGGER.info("{} - got base64 echo data: '{}' ({}={}; {}={})", "OPACFileBusinessRulesValidator.validateEchoData", base64EchoData.toString(), "totalRecords", base64EchoData.getTotalRecords(),
				"totalAmount", base64EchoData.getTotalAmount());

		// validate total lines
		long totalLinesSent = base64EchoData.getTotalRecords();
		if (!(batchTrailer.getTotalDetailLinesInBatch() == totalLinesSent && fileTrailer.getTotalDetailLinesInFile() == totalLinesSent && eigenBatch.getBatchDetails().size() == totalLinesSent)) {
			String template = "Line count mismatch: %s=%d; %s=%d; %s=%d; %s=%d";
			String formatted = String.format(template, "base64EchoData.getTotalRecords (IPAC)", totalLinesSent, "batchTrailerTotalLines (OPAC)", batchTrailer.getTotalDetailLinesInBatch(),
					"fileTrailerTotalLines (OPAC)", fileTrailer.getTotalDetailLinesInFile(), "actualFileTotalLines (OPAC)", eigenBatch.getBatchDetails().size());
			errorList.add(formatted);
		}

		// validate amounts
		BigDecimal totalAmountSent = new BigDecimal(base64EchoData.getTotalAmount());
		BigDecimal actualTotalAmount = eigenBatch.getBatchDetails().stream().map(EigenBatchDetail::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
		if (!(totalAmountSent.equals(batchTrailer.getTotalBatchAmount()) && totalAmountSent.equals(fileTrailer.getTotalFileAmount()) && totalAmountSent.equals(actualTotalAmount))) {
			String template = "Amount mismatch: %s=%s; %s=%s; %s=%s; %s=%s";
			String formatted = String.format(template, "base64EchoData.getTotalAmount (IPAC)", totalAmountSent, "batchTrailerTotalAmount (OPAC)", batchTrailer.getTotalBatchAmount(),
					"fileTrailerTotalAmount (OPAC)", fileTrailer.getTotalFileAmount(), "actualFileTotalAmount (OPAC)", actualTotalAmount);
			errorList.add(formatted);
		}
	}

	/**
	 * Method to validate batches in incoming file
	 *
	 * @param eigenBatch contains batch header, records and batch trailer
	 * @param errorList  list to track the validations errors
	 */
	private void validateBatch(EigenBatch eigenBatch, List<String> errorList) {
		LOGGER.info("OPAC Validate batch process started");
		List<EigenBatchDetail> batchDetails = eigenBatch.getBatchDetails();

		BigDecimal totalBatchAmountPaid = BigDecimal.ZERO.setScale(2);
		BigDecimal trailerBatchAmount = eigenBatch.getBatchTrailer().getTotalBatchAmount().setScale(2);

		for (EigenBatchDetail eigenBatchDetail : batchDetails) {
			totalBatchAmountPaid = totalBatchAmountPaid.add(eigenBatchDetail.getAmount().setScale(2));
			switch (eigenBatchDetail.getPaymentType()) {
			case "P":
			case "R":
			case "D":
				break;
			default:
				errorList.add("Invalid paymentType:" + eigenBatchDetail.getPaymentType() + " for customerId:" + eigenBatchDetail.getCustId());
			}

			Set<String> validPaymentTypeSet = new HashSet<String>() {
				private static final long serialVersionUID = 1L;
				{
					add("VI");
					add("MC");
					add("AX");
				}
			};
			String extOpId = eigenBatchDetail.getExtOpId();
			if (extOpId == null || extOpId.trim().length() < 2 || !validPaymentTypeSet.contains(extOpId.trim().substring(0, 2).toUpperCase())) {
				String messageTemplate = "Credit Card type is invalid: " + "(Received Credit Card type=%s), (Expected Credit Card type = VI,MC,AX)";
				String formattedMessage = String.format(messageTemplate, extOpId);
				errorList.add(formattedMessage);
			}

		}

		if (!totalBatchAmountPaid.equals(trailerBatchAmount)) {
			errorList.add(" TotalBatchAmountPaid from BatchDetails != batch trailer's TotalBatchAmount:" + totalBatchAmountPaid + "!=" + trailerBatchAmount);
		}

		if (batchDetails.size() != eigenBatch.getBatchTrailer().getTotalDetailLinesInBatch()) {
			errorList.add(" BatchDetails.size != batch trailer's Total Detail Lines In Batch" + batchDetails.size() + "!=" + eigenBatch.getBatchTrailer().getTotalDetailLinesInBatch());
		}
		LOGGER.info("OPAC Validate batch process ended");

	}

	/**
	 * Method to validate file details in incoming file
	 *
	 * @param eigenRootGroup is an instance of EigenRootGroup
	 * @param errorList      list to track the validations errors
	 */
	private void validateFile(EigenRootGroup eigenRootGroup, List<String> errorList) {

		LOGGER.info("OPAC Validate file records process started");

		BigDecimal totalBatchAmount = BigDecimal.ZERO.setScale(2);
		long totalDetailLinesInBatch = 0;

		EigenBatch eigenBatch = eigenRootGroup.getBatches().get(0);
		totalBatchAmount = totalBatchAmount.add(eigenBatch.getBatchTrailer().getTotalBatchAmount().setScale(2));
		totalDetailLinesInBatch = eigenBatch.getBatchTrailer().getTotalDetailLinesInBatch();

		BigDecimal totalFileAmount = eigenRootGroup.getFileTrailer().getTotalFileAmount();
		if (totalBatchAmount.compareTo(totalFileAmount) != 0) {
			errorList.add("totalBatchAmount from batches != file trailer's TotalFileAmount:" + totalBatchAmount + "!=" + totalFileAmount);
		}
		if (totalDetailLinesInBatch != eigenRootGroup.getFileTrailer().getTotalDetailLinesInFile()) {
			errorList.add("totalDetailLinesInBatch from batches != file trailer's Total Details lines in File" + totalDetailLinesInBatch + "!="
					+ eigenRootGroup.getFileTrailer().getTotalDetailLinesInFile());
		}
		LOGGER.info("OPAC Validate file records process ended");
	}
}
