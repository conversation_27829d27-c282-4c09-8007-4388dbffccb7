package com.etr407.erp.services.esb.subscriber.payment.opacfileconverter;

import com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.exception.ValidationException;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Camel processor class used to determine the next SAP lot ID.
 */
public class SapFileNameAnalyzer implements Processor {

    private static final Logger LOGGER = LoggerFactory.getLogger(SapFileNameAnalyzer.class);

    public void process(Exchange ex) throws ValidationException {
        LOGGER.info("SapFileNameAnalyzer.process method started");

        //Incoming file name will look something like:
        //"AX_SAP_${exchangeProperty.nextSeqNum}_${exchangeProperty.trackingId}"
        // the last suffix also has an underscore
        //We want to extract the SAP Lot id and tracking number for processing in this context

        String fileName = (String) ex.getIn().getHeader("CamelFileName");
        if(fileName != null){
            String[] parts = fileName.split("_");
            if(parts.length > 4){
                int nextSeqNum = Integer.parseInt(parts[2]);
                String trackingId = parts[3] + "_" + parts[4]; // parts[4] already has the file extension
                LOGGER.info("SAP nextSeqNum is " + nextSeqNum);
                LOGGER.info("trackingId is " + trackingId);
                ex.setProperty("nextSeqNum", nextSeqNum);
                ex.setProperty("trackingId", trackingId);
            }else{
                throw new ValidationException("File name format not correct");
            }
        } else {
            throw new ValidationException("No file name specified");
        }
        LOGGER.info("SapFileNameAnalyzer.process method ended");
    }
}
