package com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.sap;

import java.math.BigDecimal;
import java.util.Date;

public class BFKKZK {

	private String STYPE = "1";
	private String TBNAM;
	private String KEYZ1;
	private String KEYZ2;
	private String FIKEY;
	private String BVRKO;
    private String BUKRS;
	private String GSBER;
	private String BLART;
	private String WAERS;
	private String KURSF;
	private Date BUDAT;
	private Date BLDAT;
	private Date VALUT;
	private String XEIPH;
	private String AUGRD;
	private String XEBOK;
	private String XPOSA;
	private String XSCHS;
	private String INFOF;
	private BigDecimal KTSUS = BigDecimal.ZERO.movePointLeft(2);
	private BigDecimal KTSUH = BigDecimal.ZERO.movePointLeft(2);
	private int KSUMP;
	private String XCRDS;
	private String XZAUS;
	private String CCZAH;
	private String XNSEB;
	private String CVSCD;
	private String PRCTR;
	private String KUKEY;
	private String LTYPE;

	/**
	 * @return the sTYPE
	 */
	public String getSTYPE() {
		return STYPE;
	}

	/**
	 * @param sTYPE
	 *            the sTYPE to set
	 */
	public void setSTYPE(String sTYPE) {
		STYPE = sTYPE;
	}

	/**
	 * @return the tBNAM
	 */
	public String getTBNAM() {
		return TBNAM;
	}

	/**
	 * @param tBNAM
	 *            the tBNAM to set
	 */
	public void setTBNAM(String tBNAM) {
		TBNAM = tBNAM;
	}

	/**
	 * @return the kEYZ1
	 */
	public String getKEYZ1() {
		return KEYZ1;
	}

	/**
	 * @param kEYZ1
	 *            the kEYZ1 to set
	 */
	public void setKEYZ1(String kEYZ1) {
		KEYZ1 = kEYZ1;
	}

	/**
	 * @return the kEYZ2
	 */
	public String getKEYZ2() {
		return KEYZ2;
	}

	/**
	 * @param kEYZ2
	 *            the kEYZ2 to set
	 */
	public void setKEYZ2(String kEYZ2) {
		KEYZ2 = kEYZ2;
	}

	/**
	 * @return the fIKEY
	 */
	public String getFIKEY() {
		return FIKEY;
	}

	/**
	 * @param fIKEY
	 *            the fIKEY to set
	 */
	public void setFIKEY(String fIKEY) {
		FIKEY = fIKEY;
	}

	/**
	 * @return the bVRKO
	 */
	public String getBVRKO() {
		return BVRKO;
	}

	/**
	 * @param bVRKO
	 *            the bVRKO to set
	 */
	public void setBVRKO(String bVRKO) {
		BVRKO = bVRKO;
	}

	/**
	 * @return the bUKRS
	 */
	public String getBUKRS() {
		return BUKRS;
	}

	/**
	 * @param bUKRS
	 *            the bUKRS to set
	 */
	public void setBUKRS(String bUKRS) {
		BUKRS = bUKRS;
	}

	/**
	 * @return the gSBER
	 */
	public String getGSBER() {
		return GSBER;
	}

	/**
	 * @param gSBER
	 *            the gSBER to set
	 */
	public void setGSBER(String gSBER) {
		GSBER = gSBER;
	}

	/**
	 * @return the bLART
	 */
	public String getBLART() {
		return BLART;
	}

	/**
	 * @param bLART
	 *            the bLART to set
	 */
	public void setBLART(String bLART) {
		BLART = bLART;
	}

	/**
	 * @return the wAERS
	 */
	public String getWAERS() {
		return WAERS;
	}

	/**
	 * @param wAERS
	 *            the wAERS to set
	 */
	public void setWAERS(String wAERS) {
		WAERS = wAERS;
	}

	/**
	 * @return the kURSF
	 */
	public String getKURSF() {
		return KURSF;
	}

	/**
	 * @param kURSF
	 *            the kURSF to set
	 */
	public void setKURSF(String kURSF) {
		KURSF = kURSF;
	}

	/**
	 * @return the bUDAT
	 */
	public Date getBUDAT() {
		return BUDAT;
	}

	/**
	 * @param bUDAT
	 *            the bUDAT to set
	 */
	public void setBUDAT(Date bUDAT) {
		BUDAT = bUDAT;
	}

	/**
	 * @return the bLDAT
	 */
	public Date getBLDAT() {
		return BLDAT;
	}

	/**
	 * @param bLDAT
	 *            the bLDAT to set
	 */
	public void setBLDAT(Date bLDAT) {
		BLDAT = bLDAT;
	}

	/**
	 * @return the vALUT
	 */
	public Date getVALUT() {
		return VALUT;
	}

	/**
	 * @param vALUT
	 *            the vALUT to set
	 */
	public void setVALUT(Date vALUT) {
		VALUT = vALUT;
	}

	/**
	 * @return the xEIPH
	 */
	public String getXEIPH() {
		return XEIPH;
	}

	/**
	 * @param xEIPH
	 *            the xEIPH to set
	 */
	public void setXEIPH(String xEIPH) {
		XEIPH = xEIPH;
	}

	/**
	 * @return the aUGRD
	 */
	public String getAUGRD() {
		return AUGRD;
	}

	/**
	 * @param aUGRD
	 *            the aUGRD to set
	 */
	public void setAUGRD(String aUGRD) {
		AUGRD = aUGRD;
	}

	/**
	 * @return the xEBOK
	 */
	public String getXEBOK() {
		return XEBOK;
	}

	/**
	 * @param xEBOK
	 *            the xEBOK to set
	 */
	public void setXEBOK(String xEBOK) {
		XEBOK = xEBOK;
	}

	/**
	 * @return the xPOSA
	 */
	public String getXPOSA() {
		return XPOSA;
	}

	/**
	 * @param xPOSA
	 *            the xPOSA to set
	 */
	public void setXPOSA(String xPOSA) {
		XPOSA = xPOSA;
	}

	/**
	 * @return the xSCHS
	 */
	public String getXSCHS() {
		return XSCHS;
	}

	/**
	 * @param xSCHS
	 *            the xSCHS to set
	 */
	public void setXSCHS(String xSCHS) {
		XSCHS = xSCHS;
	}

	/**
	 * @return the iNFOF
	 */
	public String getINFOF() {
		return INFOF;
	}

	/**
	 * @param iNFOF
	 *            the iNFOF to set
	 */
	public void setINFOF(String iNFOF) {
		INFOF = iNFOF;
	}

	/**
	 * @return the kTSUS
	 */
	public BigDecimal getKTSUS() {
		return KTSUS;
	}

	/**
	 * @param kTSUS
	 *            the kTSUS to set
	 */
	public void setKTSUS(BigDecimal kTSUS) {
		KTSUS = kTSUS;
	}

	/**
	 * @return the kTSUH
	 */
	public BigDecimal getKTSUH() {
		return KTSUH;
	}

	/**
	 * @param kTSUH
	 *            the kTSUH to set
	 */
	public void setKTSUH(BigDecimal kTSUH) {
		KTSUH = kTSUH;
	}

	/**
	 * @return the kSUMP
	 */
	public int getKSUMP() {
		return KSUMP;
	}

	/**
	 * @param kSUMP
	 *            the kSUMP to set
	 */
	public void setKSUMP(int kSUMP) {
		KSUMP = kSUMP;
	}

	/**
	 * @return the xCRDS
	 */
	public String getXCRDS() {
		return XCRDS;
	}

	/**
	 * @param xCRDS
	 *            the xCRDS to set
	 */
	public void setXCRDS(String xCRDS) {
		XCRDS = xCRDS;
	}

	/**
	 * @return the xZAUS
	 */
	public String getXZAUS() {
		return XZAUS;
	}

	/**
	 * @param xZAUS
	 *            the xZAUS to set
	 */
	public void setXZAUS(String xZAUS) {
		XZAUS = xZAUS;
	}

	/**
	 * @return the cCZAH
	 */
	public String getCCZAH() {
		return CCZAH;
	}

	/**
	 * @param cCZAH
	 *            the cCZAH to set
	 */
	public void setCCZAH(String cCZAH) {
		CCZAH = cCZAH;
	}

	/**
	 * @return the xNSEB
	 */
	public String getXNSEB() {
		return XNSEB;
	}

	/**
	 * @param xNSEB
	 *            the xNSEB to set
	 */
	public void setXNSEB(String xNSEB) {
		XNSEB = xNSEB;
	}

	/**
	 * @return the cVSCD
	 */
	public String getCVSCD() {
		return CVSCD;
	}

	/**
	 * @param cVSCD
	 *            the cVSCD to set
	 */
	public void setCVSCD(String cVSCD) {
		CVSCD = cVSCD;
	}

	/**
	 * @return the pRCTR
	 */
	public String getPRCTR() {
		return PRCTR;
	}

	/**
	 * @param pRCTR
	 *            the pRCTR to set
	 */
	public void setPRCTR(String pRCTR) {
		PRCTR = pRCTR;
	}

	/**
	 * @return the kUKEY
	 */
	public String getKUKEY() {
		return KUKEY;
	}

	/**
	 * @param kUKEY
	 *            the kUKEY to set
	 */
	public void setKUKEY(String kUKEY) {
		KUKEY = kUKEY;
	}

	/**
	 * @return the lTYPE
	 */
	public String getLTYPE() {
		return LTYPE;
	}

	/**
	 * @param lTYPE
	 *            the lTYPE to set
	 */
	public void setLTYPE(String lTYPE) {
		LTYPE = lTYPE;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		return "BFKKZK [STYPE=" + STYPE + ", TBNAM=" + TBNAM + ", KEYZ1=" + KEYZ1 + ", KEYZ2=" + KEYZ2 + ", FIKEY=" + FIKEY + ", BVRKO=" + BVRKO + ", BUKRS=" + BUKRS + ", GSBER=" + GSBER + ", BLART="
				+ BLART + ", WAERS=" + WAERS + ", KURSF=" + KURSF + ", BUDAT=" + BUDAT + ", BLDAT=" + BLDAT + ", VALUT=" + VALUT + ", XEIPH=" + XEIPH + ", AUGRD=" + AUGRD + ", XEBOK=" + XEBOK
				+ ", XPOSA=" + XPOSA + ", XSCHS=" + XSCHS + ", INFOF=" + INFOF + ", KTSUS=" + KTSUS + ", KTSUH=" + KTSUH + ", KSUMP=" + KSUMP + ", XCRDS=" + XCRDS + ", XZAUS=" + XZAUS + ", CCZAH="
				+ CCZAH + ", XNSEB=" + XNSEB + ", CVSCD=" + CVSCD + ", PRCTR=" + PRCTR + ", KUKEY=" + KUKEY + ", LTYPE=" + LTYPE + "]";
	}

}
