<!-- edited with XMLSpy v2021 (x64) (http://www.altova.com) by <PERSON> (407ETR) -->
<beanio xmlns="http://www.beanio.org/2012/03" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.beanio.org/2012/03 http://www.beanio.org/2012/03/mapping.xsd">
	<stream name="sapData" format="fixedlength" strict="true" mode="write">
		<group name="sapRootGroup" minOccurs="1" maxOccurs="1" order="1" class="com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.sap.SAPRootGroup">
			<record name="BFKKZGR00" class="com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.sap.BFKKZGR00" order="1" minLength="5" maxLength="5" minOccurs="1" maxOccurs="1">
				<field name="STYPE" length="1" rid="true" literal="0" required="true"/>
				<field name="MANDT" length="3" required="true"/>
				<field name="APPLK" length="1" required="true" literal="S"/>
			</record>
			<record name="BFKKZK" class="com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.sap.BFKKZK" order="2" minLength="273" maxLength="273" minOccurs="0" maxOccurs="1">
				<field name="STYPE" length="1" rid="true" literal="1" required="true"/>
				<field name="TBNAM" length="30" required="true" literal="BFKKZK"/>
				<field name="KEYZ1" length="12" required="true"/>
				<field name="KEYZ2" length="40" required="true"/>
				<field name="FIKEY" length="12" required="true"/>
				<field name="BVRKO" length="10" required="true" literal="0000011120"/>
				<field name="BUKRS" length="4" required="true" literal="407"/>
				<field name="GSBER" length="4" required="true" literal="ETR"/>
				<field name="BLART" length="2" required="true"/>
				<field name="WAERS" length="5" required="true" literal="CAD"/>
				<field name="KURSF" length="10" required="true"/>
				<field name="BUDAT" length="8" required="true" format="yyyyMMdd" />
				<field name="BLDAT" length="8" required="true" format="yyyyMMdd" />
				<field name="VALUT" length="8" required="true" format="yyyyMMdd" />
				<field name="XEIPH" length="1" required="true"/>
				<field name="AUGRD" length="2" required="true" literal="01"/>
				<field name="XEBOK" length="1" required="true"/>
				<field name="XPOSA" length="1" required="true"/>
				<field name="XSCHS" length="1" required="true"/>
				<field name="INFOF" length="50" required="true"/>
				<field name="KTSUS" length="15" required="true"/>
				<field name="KTSUH" length="15" required="true"/>
				<field name="KSUMP" length="6" required="true" padding="0" justify="right"/>
				<field name="XCRDS" length="1" required="true"/>
				<field name="XZAUS" length="1" required="true" literal="X"/>
				<field name="CCZAH" length="1" required="true"/>
				<field name="XNSEB" length="1" required="true"/>
				<field name="CVSCD" length="3" required="true"/>
				<field name="PRCTR" length="10" required="true"/>
				<field name="KUKEY" length="8" required="true"/>
				<field name="LTYPE" length="2" required="true"/>
			</record>
			<record name="BFKKZPList" class="com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.sap.BFKKZP" order="3" minLength="723" maxLength="723" minOccurs="0" collection="list">
				<field name="STYPE" length="1" rid="true" literal="2" required="true"/>
				<field name="TBNAM" length="30" required="true" literal="BFKKZP"/>
				<field name="SELT1" length="1" required="true" literal="U"/>
				<field name="SELT2" length="1" required="true"/>
				<field name="SELT3" length="1" required="true"/>
				<field name="SELW1" length="35" required="true"/>
				<field name="SELW2" length="35" required="true"/>
				<field name="SELW3" length="35" required="true"/>
				<field name="BETRZ" length="16" required="true"/>
				<field name="BETRH" length="16" required="true"/>
				<field name="TBETR" length="16" required="true"/>
				<field name="BVRKO" length="10" required="true" literal="0000011120"/>
				<field name="BUKRS" length="4" required="true" literal="407"/>
				<field name="GSBER" length="4" required="true" literal="ETR"/>
				<field name="BLART" length="2" required="true"/>
				<field name="WAERS" length="5" required="true" literal="CAD"/>
				<field name="KURSF" length="10" required="true"/>
				<field name="BUDAT" length="8" required="true" format="yyyyMMdd" />
				<field name="BLDAT" length="8" required="true" format="yyyyMMdd" />
				<field name="VALUT" length="8" required="true" format="yyyyMMdd" />
				<field name="XEIPH" length="1" required="true"/>
				<field name="AUGRD" length="2" required="true" literal="01"/>
				<field name="XAKON" length="1" required="true"/>
				<field name="XKLAE" length="1" required="true"/>
				<field name="KLAEH" length="10" required="true"/>
				<field name="TXTVW" length="80" required="true"/>
				<field name="CHCKN" length="13" required="true"/>
				<field name="BANKS" length="3" required="true"/>
				<field name="BANKL" length="15" required="true"/>
				<field name="BANKN" length="18" required="true"/>
				<field name="BKONT" length="2" required="true"/>
				<field name="KOINH" length="60" required="true"/>
				<field name="XPGRO" length="1" required="true"/>
				<field name="XDAUB" length="1" required="true"/>
				<field name="INFOF" length="50" required="true"/>
				<field name="KUKON" length="4" required="true"/>
				<field name="BKREF" length="20" required="true"/>
				<field name="CCINS" length="4" required="true"/>
				<field name="CCNUM" length="25" required="true"/>
				<field name="DATAB" length="8" required="true" format="yyyyMMdd" />
				<field name="DATBI" length="8" required="true" format="yyyyMMdd" />
				<field name="AUNUM" length="25" required="true"/>
				<field name="AUDAT" length="8" required="true" format="yyyyMMdd" />
				<field name="AUTIM" length="6" required="true"/>
				<field name="PRCTR" length="10" required="true"/>
				<field name="BEGRU" length="4" required="true"/>
				<field name="MERCH" length="15" required="true"/>
				<field name="IBAN" length="34" required="true"/>
				<field name="SWIFT" length="11" required="true"/>
				<field name="ESNUM" length="5" required="true"/>
				<field name="LNKID" length="32" required="true"/>
			</record>
		</group>
	</stream>
</beanio>
