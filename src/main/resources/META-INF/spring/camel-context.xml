<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="
		http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans.xsd
		http://camel.apache.org/schema/spring
		http://camel.apache.org/schema/spring/camel-spring-2.16.1.xsd">
	<bean class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
		<property name="locations">
			<list>
				<value>file:${file.separator}407${file.separator}apps${file.separator}ETR407Conf${file.separator}${project.artifactId}${file.separator}application.properties</value>
			</list>
		</property>
	</bean>
	<!-- Needed for Camel Context properties -->
	<bean id="properties" class="org.apache.camel.component.properties.PropertiesComponent">
		<property name="location" value="file:${file.separator}407${file.separator}apps${file.separator}ETR407Conf${file.separator}ERP.Subscriber.Payment.OPACFileConverter${file.separator}application.properties" />
	</bean>
	<bean id="metricRegistry" class="com.codahale.metrics.MetricRegistry" />
	<bean id="metricsRoutePolicyFactory" class="org.apache.camel.component.metrics.routepolicy.MetricsRoutePolicyFactory">
		<property name="metricsRegistry" ref="metricRegistry" />
		<property name="useJmx" value="true" />
	</bean>
	<bean id="metricsRegistryService" class="org.apache.camel.component.metrics.routepolicy.MetricsRegistryService">
		<property name="metricsRegistry" ref="metricRegistry" />
		<property name="useJmx" value="true" />
	</bean>
	<bean id="sapFileNameAnalyzer" class="com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.SapFileNameAnalyzer" />
	<bean id="sapLotIdGenerator" class="com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.SAPLotIdGenerator" />
	<bean id="filenameProcessor" class="com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.FilenameProcessor" />
	<bean id="opacFileConverterRoutePolicy" class="org.apache.camel.component.zookeeper.policy.ZooKeeperRoutePolicy">
		<constructor-arg type="java.lang.String" value="zookeeper:${zookeeper.cluster}/${zookeeper.zNode}?create=true" />
		<constructor-arg type="int" value="1" />
		<property name="shouldStopConsumer" value="false" />
	</bean>
	<bean id="oAuthValidationProcessor" class="com.etr407.common.oauthvalidation.OAuthValidationProcessor">
		<property name="scope" value="${oauth.requiredScope}" />
	</bean>
	<bean id="noFilesAvailableException" class="com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.exception.NoFilesAvailableException">
		<constructor-arg index="0" value="No files found at the vendor site." />
	</bean>
	<bean id="myUuid" class="com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.utils.UuidGenerator" />
	<bean id="opacFailureProcessor" class="com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.OPACFailureProcessor" />
	<bean id="opacFileBusinessRulesValidator" class="com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.OPACFileBusinessRulesValidator" />
	<bean id="duplicateFileProcessor" class="com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.DuplicateFileProcessor">
		<constructor-arg type="java.lang.String" value="${router.done}" />
		<constructor-arg type="int" value="${duplicate.fileCheck.age}" />
	</bean>
	<bean id="mapper" class="com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.mapping.EigenToSapMapper">
		<constructor-arg type="java.lang.String" value="${sap.client}" />
	</bean>
	<bean id="declinedMapper" class="com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.mapping.EigenToSapDeclinedMapper">
		<constructor-arg type="java.lang.String" value="${sap.client}" />
	</bean>
	<bean id="router" class="com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.routing.Router" />

	<!-- Camel -->
	<camelContext id="ERP.Subscriber.Payment.OPACFileConverter.camel" xmlns="http://camel.apache.org/schema/spring" autoStartup="true" errorHandlerRef="errorHandler" streamCache="true">
		<properties>
			<property key="CamelLogDebugBodyMaxChars" value="11500" />
		</properties>
		<errorHandler id="errorHandler" type="DeadLetterChannel" deadLetterUri="direct:deadLetterQueue" useOriginalMessage="true" onPrepareFailureRef="opacFailureProcessor">
			<redeliveryPolicy maximumRedeliveries="0" retryAttemptedLogLevel="INFO" />
		</errorHandler>
		<dataFormats>
			<beanio id="eigenBeanio" mapping="beanio-mappings.eigen.xml" streamName="eigenData" encoding="UTF-8" />
			<beanio id="sapBeanio" mapping="beanio-mappings.sap.xml" streamName="sapData" encoding="UTF-8" />
			<beanio id="sapDeclinedBeanio" mapping="beanio-mappings.sap.declined.xml" streamName="SAPDeclinedData" encoding="UTF-8" />
			<json id="jsonFormatAwsResponse" library="Jackson" unmarshalTypeName="com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.aws.PaymentHubMetaDataResponse" />
			<json id="jsonValidationResponse" library="Jackson" unmarshalTypeName="java.util.List" />
			<json id="jsonExceptionResponse" library="Jackson" unmarshalTypeName="java.lang.String" />
		</dataFormats>
		<!-- EXCEPTION HANDLING -->
		<onException>
			<exception>java.lang.IllegalStateException</exception>
			<onWhen>
				<simple>${exception.message} contains 'Zookeeper based route policy prohibits'</simple>
			</onWhen>
			<redeliveryPolicy maximumRedeliveries="0" logExhaustedMessageHistory="false" logExhausted="false" logStackTrace="false" logRetryAttempted="false" />
			<handled>
				<constant>true</constant>
			</handled>
			<log message="Zookeeper: ${exception.message}" loggingLevel="DEBUG" />
		</onException>
		<onException id="oauth_validation_exception">
			<exception>com.etr407.common.oauthvalidation.OAuthValidationException</exception>
			<handled>
				<constant>true</constant>
			</handled>
			<setHeader headerName="Exchange.HTTP_RESPONSE_CODE">
				<constant>401</constant>
			</setHeader>
			<setBody>
				<simple>{"errorMessage":"Invalid or missing OAuth token"}</simple>
			</setBody>
			<log message="oauth validation exception: ${exception}" loggingLevel="ERROR" />
		</onException>
		<!-- Needed due an apparent bug in Camel. Files aren't moved on errors otherwise. -->
		<onException useOriginalMessage="true">
			<exception>java.lang.Exception</exception>
			<to uri="direct:deadLetterQueue" />
		</onException>
		<!-- INTERCEPTS -->
		<interceptFrom uri="direct:init*">
			<when>
				<simple>${exchangeProperty.trackingId} == null || ${exchangeProperty.trackingId} == ''</simple>
				<setProperty propertyName="X-407ETR-PROCESSID">
					<simple>bean:myUuid?method=generateUuid</simple>
				</setProperty>
				<setProperty propertyName="X-407ETR-CORRELATIONID">
					<simple>bean:myUuid?method=generateUuid</simple>
				</setProperty>
				<setProperty propertyName="trackingId">
					<simple>${exchangeProperty.X-407ETR-PROCESSID}_${exchangeProperty.X-407ETR-CORRELATIONID}
					</simple>
				</setProperty>
			</when>
			<process ref="oAuthValidationProcessor" />
		</interceptFrom>
		<!-- REST CONFIGURATION -->
		<restConfiguration component="servlet" contextPath="/apis" apiContextPath="/erp/v1/OPACFileConverter/start/api-doc" apiContextRouteId="ERP.Subscriber.Payment.OPACFileConverter.initFetch.v1.ApiDoc" bindingMode="off">
			<dataFormatProperty key="prettyPrint" value="true" />
			<apiProperty key="api.title" value="OPACFileConverter - File fetch initiation endpoint" />
			<apiProperty key="api.description" value="Service to start SAP payment file fetching to send the data to Eigen" />
			<apiProperty key="api.contact.name" value="<EMAIL>" />
		</restConfiguration>
		<rest path="/erp/v1/health/OPACFileConverter" consumes="application/json" produces="application/json">
			<get id="opacFileConverterHealthCheck" uri="/" produces="application/json">
				<description>Payment endpoint health check</description>
				<responseMessage code="200" message="The backing ESB bundle is active" />
				<to uri="direct:healthCheck" />
			</get>
		</rest>
		<rest path="/erp/v1/OPACFileConverter" consumes="application/json" produces="application/json">
			<post id="start" uri="/start" produces="application/json">
				<description>Start OPACFileConverter process</description>
				<responseMessage code="200" message="File fetch initiated" />
				<responseMessage code="400" message="Invalid input data" />
				<responseMessage code="401" message="Invalid access token" />
				<responseMessage code="500" message="Internal server error" />
				<to uri="direct:initFetch" />
			</post>
			<post id="router" uri="/router" produces="application/json">
				<description>Start OPACFileConverter process</description>
				<responseMessage code="200" message="File processing initiated" />
				<responseMessage code="400" message="Invalid input data" />
				<responseMessage code="401" message="Invalid access token" />
				<responseMessage code="500" message="Internal server error" />
				<to uri="direct:initRouter" />
			</post>
			<post id="validate" uri="/validate" produces="application/json">
				<description>validate file</description>
				<responseMessage code="200" message="File validated successfully" />
				<responseMessage code="400" message="Invalid input data" />
				<responseMessage code="500" message="Internal server error" />
				<to uri="direct:validate" />
			</post>
		</rest>
		<route id="ERP.Subscriber.Payment.OPACFileConverter.healthCheck">
			<from uri="direct:healthCheck" />
			<setHeader headerName="Exchange.HTTP_RESPONSE_CODE">
				<constant>200</constant>
			</setHeader>
			<setHeader headerName="Exchange.CONTENT_TYPE">
				<constant>text/plain</constant>
			</setHeader>
			<setBody>
				<constant>${project.artifactId} ${project.version} OK</constant>
			</setBody>
		</route>
		<!-- Validation -->
		<route id="ERP.Subscriber.Payment.OPACFileConverter.validate">
			<from uri="direct:validate" />
			<removeHeaders pattern="*" />
			<choice>
				<when>
					<simple>${properties-location:validation.feature.flag} != null &amp;&amp; ${properties-location:validation.feature.flag} == 'true'</simple>
					<doTry>
						<convertBodyTo type="java.lang.String" />
						<setProperty propertyName="unmarshalledPaymentsFile">
							<simple>${body}</simple>
						</setProperty>
						<!-- you may need to get the next sequence number for the validation bean -->
						<log loggingLevel="INFO" message="Getting next sequence number" />
						<to uri="token:oauth?clientId=RAW({{aws.client.id}})&amp;clientSecret=RAW({{aws.client.secret}})&amp;scope=RAW({{aws.client.scope}})" />
						<removeHeaders pattern="CamelHttp*" />
						<setBody>
							<constant>null</constant>
						</setBody>
						<setHeader headerName="CamelHttpMethod">
							<constant>GET</constant>
						</setHeader>
						<to uri="https4:{{aws.paymentHubMetaData.endpoint}}OPACFileConverter?connectTimeout={{aws.connectTimeout}}&amp;requestTimeout={{aws.requestTimeout}}&amp;throwExceptionOnFailure=true&amp;bridgeEndpoint=true" id="TO_AWS_REST_VALIDATE" />
						<log loggingLevel="INFO" message="Received GET response from metadata API: ${body}" />
						<unmarshal ref="jsonFormatAwsResponse" />
						<setProperty propertyName="nextSeqNum">
							<simple>${body.nextSeqNum}</simple>
						</setProperty>
						<setBody>
							<simple>${exchangeProperty.unmarshalledPaymentsFile}</simple>
						</setBody>
						<!-- validate -->
						<to uri="metrics:timer:ERP.Subscriber.Payment.OPACFileConverter.validate.timer?action=start" />
						<unmarshal ref="eigenBeanio" />
						<to uri="metrics:timer:ERP.Subscriber.Payment.OPACFileConverter.validate.timer?action=stop" />
						<setProperty propertyName="validateOnly">
							<simple resultType="java.lang.Boolean">true</simple>
						</setProperty>
						<process ref="opacFileBusinessRulesValidator" />
						<marshal ref="jsonValidationResponse" />
						<doCatch>
							<exception>org.beanio.BeanIOException</exception>
							<setBody>
								<simple>${exception.message}</simple>
							</setBody>
							<!--make string json compatible -->
							<marshal ref="jsonExceptionResponse" />
							<setBody>
								<simple>[${body}]</simple>
							</setBody>
						</doCatch>
						<doCatch>
							<exception>org.apache.camel.InvalidPayloadException</exception>
							<setBody>
								<constant>Invalid file</constant>
							</setBody>
							<!--make string json compatible -->
							<marshal ref="jsonExceptionResponse" />
							<setBody>
								<simple>[${body}]</simple>
							</setBody>
						</doCatch>
						<doFinally>
							<setHeader headerName="Exchange.HTTP_RESPONSE_CODE">
								<constant>200</constant>
							</setHeader>
							<setHeader headerName="Exchange.CONTENT_TYPE">
								<constant>application/json</constant>
							</setHeader>
							<setBody>
								<simple>{"errors": ${body}}</simple>
							</setBody>
						</doFinally>
					</doTry>
				</when>
				<otherwise>
					<setHeader headerName="Exchange.HTTP_RESPONSE_CODE">
						<constant>403</constant>
					</setHeader>
					<setHeader headerName="Exchange.CONTENT_TYPE">
						<constant>application/json</constant>
					</setHeader>
					<setBody>
						<simple>{"errorMessage": "Forbidden"}</simple>
					</setBody>
				</otherwise>
			</choice>
		</route>
		<!-- File pull initiation -->
		<route id="ERP.Subscriber.Payment.OPACFileConverter.initFetch">
			<!-- start pull file -->
			<from uri="direct:initFetch" />
			<setBody>
				<simple>${null}</simple>
			</setBody>
			<removeHeaders pattern="*" />
			<to uri="seda:getFile?exchangePattern=InOnly" />
			<log message="File pull initiated" loggingLevel="INFO" />
			<!-- create response -->
			<setHeader headerName="Content-Type">
				<constant>application/json</constant>
			</setHeader>
			<setBody>
				<simple>{"message": "File fetching initiated"}</simple>
			</setBody>
		</route>
		<route id="ERP.Subscriber.Payment.OPACFileConverter.initRouter">
			<from uri="direct:initRouter" />
			<setBody>
				<simple>${null}</simple>
			</setBody>
			<removeHeaders pattern="*" />
			<to uri="seda:startRouter?exchangePattern=InOnly" />
			<log message="File processing initiated" loggingLevel="INFO" />
			<!-- create response -->
			<setHeader headerName="Content-Type">
				<constant>application/json</constant>
			</setHeader>
			<setBody>
				<simple>{"message": "File processing initiated"}</simple>
			</setBody>
		</route>
		<!-- Get file -->
		<route id="ERP.Subscriber.Payment.OPACFileConverter.getFile">
			<from uri="seda:getFile" />
			<log message="Getting file" loggingLevel="INFO" />
			<!-- get file list -->
			<to uri="token:oauth?clientId={{oauth.esbClientId}}&amp;clientSecret={{oauth.esbClientSecret}}&amp;scope={{oauth.esbScope}}" />
			<setHeader headerName="CamelHttpMethod">
				<constant>GET</constant>
			</setHeader>
			<to uri="metrics:timer:ERP.Subscriber.Payment.OPACFileConverter.getFile:httpGetFileList.timer?action=start" />
			<to uri="{{proxy.fileList.uri}}?throwExceptionOnFailure=true" />
			<to uri="metrics:timer:ERP.Subscriber.Payment.OPACFileConverter.getFile:httpGetFileList.timer?action=stop" />
			<log message="Received file list" loggingLevel="INFO" />
			<choice>
				<when>
					<simple>${body} != null &amp;&amp; ${body} != ''</simple>
					<split streaming="true" stopOnException="true">
						<tokenize token="\r\n|\n" xml="false" trim="true" />
						<!-- sanitize filename -->
						<bean beanType="java.net.URLEncoder" method="encode(${body}, 'UTF-8')" />
						<setHeader headerName="EncodedFilename">
							<simple>${body}</simple>
						</setHeader>
						<log message="Retrieving file: ${header.EncodedFilename}" loggingLevel="INFO" />
						<!-- get file -->
						<to uri="token:oauth?clientId={{oauth.esbClientId}}&amp;clientSecret={{oauth.esbClientSecret}}&amp;scope={{oauth.esbScope}}" />
						<setHeader headerName="CamelHttpMethod">
							<constant>GET</constant>
						</setHeader>
						<to uri="metrics:timer:ERP.Subscriber.Payment.OPACFileConverter.getFile:httpGetFile.timer?action=start" />
						<toD uri="{{proxy.file.uri}}${header.EncodedFilename}?throwExceptionOnFailure=true" />
						<to uri="metrics:timer:ERP.Subscriber.Payment.OPACFileConverter.getFile:httpGetFile.timer?action=stop" />
						<log message="Received file" loggingLevel="INFO" />
						<!-- write file -->
						<process ref="filenameProcessor" />
						<convertBodyTo type="java.lang.String" />
						<to uri="metrics:timer:ERP.Subscriber.Payment.OPACFileConverter.getFile:fileWriter.timer?action=start" />
						<to uri="file://{{router.inbox}}" />
						<to uri="metrics:timer:ERP.Subscriber.Payment.OPACFileConverter.getFile:fileWriter.timer?action=stop" />
						<log message="Saved file: {{router.inbox}}/${header.CamelFileName}" />
						<to uri="direct:deleteRemoteFile" />
					</split>
				</when>
				<otherwise>
					<throwException ref="noFilesAvailableException" />
				</otherwise>
			</choice>
		</route>
		<!-- Zookeeper workaround -->
		<route id="ERP.Subscriber.Payment.OPACFileConverter.timer" routePolicyRef="opacFileConverterRoutePolicy">
			<from uri="timer:a?period=10000" />
			<to uri="seda:startMapper?exchangePattern=InOnly" />
		</route>
		<!-- Routing -->
		<route id="ERP.Subscriber.Payment.OPACFileConverter.router">
			<from uri="seda:startRouter" />
			<pollEnrich timeout="5000">
				<constant>file://{{router.inbox}}?delay=3000&amp;readLock=changed&amp;readLockMinAge=3s&amp;readLockMinLength=0&amp;sortBy=file:name&amp;preMove=inprogress&amp;moveFailed={{router.error}}${exchangeProperty.origFileName}-${date:now:yyyyMMddHHmmssSSS}</constant>
			</pollEnrich>
			<choice>
				<when>
					<simple>${header.CamelFileName} != null &amp;&amp; ${header.CamelFileName} != ''</simple>
					<log message="Found file: ${header.CamelFileName}" loggingLevel="INFO" />
					<setProperty propertyName="origFileName">
						<simple>${header.CamelFileName}</simple>
					</setProperty>
					<setProperty propertyName="origFileContent">
						<simple>${body}</simple>
					</setProperty>
					<!-- Set Process ID for Splunk monitoring -->
					<setProperty propertyName="X-407ETR-PROCESSID">
						<simple>bean:myUuid?method=generateUuid</simple>
					</setProperty>
					<setProperty propertyName="X-407ETR-CORRELATIONID">
						<simple>bean:myUuid?method=generateUuid</simple>
					</setProperty>
					<setProperty propertyName="trackingId">
						<simple>${exchangeProperty.X-407ETR-PROCESSID}_${exchangeProperty.X-407ETR-CORRELATIONID}</simple>
					</setProperty>

					<!-- unmarshal -->
					<to uri="metrics:timer:ERP.Subscriber.Payment.OPACFileConverter.router:eigenBeanio.timer?action=start" />
					<unmarshal ref="eigenBeanio" />
					<to uri="metrics:timer:ERP.Subscriber.Payment.OPACFileConverter.router:eigenBeanio.timer?action=stop" />
					<log message="Unmarshalling complete" loggingLevel="INFO" />
					<setProperty propertyName="unmarshalledPaymentsFile">
						<simple>${body}</simple>
					</setProperty>

					<!-- get next sequence number -->
					<to uri="token:oauth?clientId=RAW({{aws.client.id}})&amp;clientSecret=RAW({{aws.client.secret}})&amp;scope=RAW({{aws.client.scope}})" />
					<removeHeaders pattern="CamelHttp*" />
					<setHeader headerName="CamelHttpMethod">
						<constant>GET</constant>
					</setHeader>
					<setBody>
						<simple>${null}</simple>
					</setBody>
					<to uri="https4:{{aws.paymentHubMetaData.endpoint}}OPACFileConverter?connectTimeout={{aws.connectTimeout}}&amp;requestTimeout={{aws.requestTimeout}}&amp;throwExceptionOnFailure=true&amp;bridgeEndpoint=true" id="TO_AWS_REST" />
					<log message="${exchangeProperty.X-407ETR-PROCESSID}_${exchangeProperty.X-407ETR-CORRELATIONID} Received response from metadata API: ${body}" loggingLevel="INFO" />
					<unmarshal ref="jsonFormatAwsResponse" />

					<!-- check if seq no is 1000, if so reset to 1 -->
					<choice>
						<when>
							<simple>${body.nextSeqNum} == 1000 </simple>
							<log loggingLevel="INFO" message="${exchangeProperty.X-407ETR-PROCESSID}_${exchangeProperty.X-407ETR-CORRELATIONID} Previous sequence number greater than 999, rolling over to 1" />
							<setProperty propertyName="nextSeqNum">
								<simple resultType="int">1</simple>
							</setProperty>
						</when>
						<otherwise>
							<log loggingLevel="INFO" message="${exchangeProperty.X-407ETR-PROCESSID}_${exchangeProperty.X-407ETR-CORRELATIONID} Continuing with sequence number ${body.nextSeqNum}" />
							<setProperty propertyName="nextSeqNum">
								<simple resultType="int">${body.nextSeqNum}</simple>
							</setProperty>
						</otherwise>
					</choice>
					
					<setBody>
						<simple>${exchangeProperty.unmarshalledPaymentsFile}</simple>
					</setBody>

					<!-- validate contents from Eigen -->
					<to uri="metrics:timer:ERP.Subscriber.Payment.OPACFileConverter.router:opacFileBusinessRulesValidator.timer?action=start" />
					<process ref="opacFileBusinessRulesValidator" />
					<to uri="metrics:timer:ERP.Subscriber.Payment.OPACFileConverter.router:opacFileBusinessRulesValidator.timer?action=stop" />
					<log message="Business rules validation complete" loggingLevel="INFO" />

					<!-- duplicate file processor checks -->
					<setBody>
						<simple>${exchangeProperty.unmarshalledPaymentsFile}</simple>
					</setBody>
					<setHeader headerName="CamelFileName">
						<simple>${exchangeProperty.origFileName}</simple>
					</setHeader>
					<process ref="duplicateFileProcessor" />
					<setProperty propertyName="processDate">
						<simple>${date:now:yyyyMMdd}</simple>
					</setProperty>

					<!-- route contents -->
					<to uri="metrics:timer:ERP.Subscriber.Payment.OPACFileConverter.router:router.timer?action=start" />
					<process ref="router" />
					<to uri="metrics:timer:ERP.Subscriber.Payment.OPACFileConverter.router:router.timer?action=stop" />
					<log message="Routing complete" loggingLevel="INFO" />

					<!-- write Declined transactions file for SAP mapper -->
					<choice>
						<when>
							<simple>${exchangeProperty.EigenRootGroupForSapDeclined} != null</simple>
							<setBody>
								<simple>${exchangeProperty.EigenRootGroupForSapDeclined}</simple>
							</setBody>
							<marshal ref="eigenBeanio" />
							<setHeader headerName="CamelFileName">
								<simple>Declined_SAP_${exchangeProperty.nextSeqNum}_${exchangeProperty.trackingId}</simple>
							</setHeader>
							<to uri="file://{{sapMapper.inbox}}" />
						</when>
						<otherwise>
							<setHeader headerName="CamelFileName">
								<simple>ctrl_opac_declines_no</simple>
							</setHeader>
							<setBody>
								<constant>No SAP declined records found.</constant>
							</setBody>
							<to uri="metrics:timer:ERP.Subscriber.Payment.OPACFileConverter.router:noCtrlFileDeclines.timer?action=start" />
							<to uri="file://{{sapMapper.declinedOutbox}}?allowNullBody=true&amp;chmod={{sapMapper.outbox.permissions}}" />
							<to uri="metrics:timer:ERP.Subscriber.Payment.OPACFileConverter.router:noCtrlFileDeclines.timer?action=stop" />
							<log message="${exchangeProperty.trackingId}: No_ctrl_file created for SAP declines" loggingLevel="INFO" />
						</otherwise>
					</choice>
					<choice>
						<when>
							<simple>${exchangeProperty.EigenRootGroupForSapAmex} == null &amp;&amp; ${exchangeProperty.EigenRootGroupForSapMastercard} == null &amp;&amp; ${exchangeProperty.EigenRootGroupForSapVisa} == null</simple>
							<setHeader headerName="CamelFileName">
								<simple>ctrl_opac_no</simple>
							</setHeader>
							<setBody>
								<constant>No SAP customers found.</constant>
							</setBody>
							<to uri="metrics:timer:ERP.Subscriber.Payment.OPACFileConverter.router:noCtrlFile.timer?action=start" />
							<to uri="file://{{sapMapper.outbox}}?allowNullBody=true&amp;chmod={{sapMapper.outbox.permissions}}" />
							<to uri="metrics:timer:ERP.Subscriber.Payment.OPACFileConverter.router:noCtrlFile.timer?action=stop" />
							<log message="${exchangeProperty.trackingId}:  No_ctrl_file created for SAP approved transactions" loggingLevel="INFO" />
						</when>
					</choice>
					<!-- write American Express file for SAP mapper -->
					<choice>
						<when>
							<simple>${exchangeProperty.EigenRootGroupForSapAmex} != null</simple>
							<setBody>
								<simple>${exchangeProperty.EigenRootGroupForSapAmex}</simple>
							</setBody>
							<marshal ref="eigenBeanio" />
							<setHeader headerName="CamelFileName">
								<simple>AX_SAP_${exchangeProperty.nextSeqNum}_${exchangeProperty.trackingId}</simple>
							</setHeader>
							<to uri="file://{{sapMapper.inbox}}" />
						</when>
					</choice>
					<!-- write MasterCard file for SAP mapper -->
					<choice>
						<when>
							<simple>${exchangeProperty.EigenRootGroupForSapMastercard} != null</simple>
							<setBody>
								<simple>${exchangeProperty.EigenRootGroupForSapMastercard}</simple>
							</setBody>
							<marshal ref="eigenBeanio" />
							<setHeader headerName="CamelFileName">
								<simple>MC_SAP_${exchangeProperty.nextSeqNum}_${exchangeProperty.trackingId}</simple>
							</setHeader>
							<to uri="file://{{sapMapper.inbox}}" />
						</when>
					</choice>
					<!-- write Visa file for SAP mapper -->
					<choice>
						<when>
							<simple>${exchangeProperty.EigenRootGroupForSapVisa} != null</simple>
							<setBody>
								<simple>${exchangeProperty.EigenRootGroupForSapVisa}</simple>
							</setBody>
							<marshal ref="eigenBeanio" />
							<setHeader headerName="CamelFileName">
								<simple>VI_SAP_${exchangeProperty.nextSeqNum}_${exchangeProperty.trackingId}</simple>
							</setHeader>
							<to uri="file://{{sapMapper.inbox}}" />
						</when>
					</choice>
					<setBody>
						<simple>${exchangeProperty.origFileContent}</simple>
					</setBody>
					<setHeader headerName="CamelFileName">
						<simple>${exchangeProperty.origFileName}-${date:now:yyyyMMddHHmmssSSS}</simple>
					</setHeader>
					<to uri="file:{{router.done}}" />

					<!-- write original file for Cash Admin -->
					<setBody>
						<simple>${exchangeProperty.origFileContent}</simple>
					</setBody>
					<setHeader headerName="CamelFileName">
						<simple>${file:name.noext}-original.${file:ext}</simple>
					</setHeader>
					<log loggingLevel="INFO" message="Writing file for Cash Admin at {{cashAdmin.dropbox}}" />
					<to uri="file://{{cashAdmin.dropbox}}?chmod={{cashAdmin.dropbox.permissions}}" />

					<!-- update Next Seq No -->
					<log message="Updating next sequence number" loggingLevel="INFO" />
					<to uri="token:oauth?clientId=RAW({{aws.client.id}})&amp;clientSecret=RAW({{aws.client.secret}})&amp;scope=RAW({{aws.client.scope}})" />
					<removeHeaders pattern="CamelHttp*" />
					<setBody>
						<simple>{ "integrationId": "OPACFileConverter", "seq": ${exchangeProperty.nextSeqNum} }</simple>
					</setBody>
					<setHeader headerName="CamelHttpMethod">
						<constant>POST</constant>
					</setHeader>
					<to uri="https4:{{aws.paymentHubMetaData.endpoint}}?connectTimeout={{aws.connectTimeout}}&amp;requestTimeout={{aws.requestTimeout}}&amp;throwExceptionOnFailure=true&amp;bridgeEndpoint=true" id="TO_AWS_REST" />
					<log message="${exchangeProperty.trackingId}: Received response from metadata API: ${body}" loggingLevel="INFO" />
				</when>
			</choice>
		</route>

		<!-- Map Eigen file to SAP format -->
		<route id="ERP.Subscriber.Payment.OPACFileConverter.mapToSAP">
			<from uri="seda:startMapper" />
			<pollEnrich timeout="5000">
				<constant>file://{{sapMapper.inbox}}?delay=3000&amp;readLock=changed&amp;readLockMinAge=3s&amp;sortBy=file:name&amp;preMove=inprogress&amp;moveFailed={{sapMapper.error}}${file:name}-${date:now:yyyyMMddHHmmssSSS}.${header.fileExt}</constant>
			</pollEnrich>
			<choice>
				<when>
					<simple>${body} != null &amp;&amp; ${body} != ''</simple>
					<setProperty propertyName="X-407ETR-PROCESSID">
						<simple>bean:myUuid?method=generateUuid</simple>
					</setProperty>
					<setProperty propertyName="X-407ETR-CORRELATIONID">
						<simple>bean:myUuid?method=generateUuid</simple>
					</setProperty>
					<setProperty propertyName="trackingId">
						<simple>${exchangeProperty.X-407ETR-PROCESSID}_${exchangeProperty.X-407ETR-CORRELATIONID}</simple>
					</setProperty>
					<setProperty propertyName="origFileName">
						<simple>${header.CamelFileName}.txt</simple>
					</setProperty>
					<setProperty propertyName="fileNameWithoutExt">
						<simple>${header.CamelFileName}</simple>
					</setProperty>
					<log message="${exchangeProperty.trackingId}: Found file: ${header.CamelFileName}" loggingLevel="INFO" />

					<!-- unmarshal -->
					<to uri="metrics:timer:ERP.Subscriber.Payment.OPACFileConverter.mapToSAP:eigenBeanio.timer?action=start" />
					<unmarshal ref="eigenBeanio" />
					<to uri="metrics:timer:ERP.Subscriber.Payment.OPACFileConverter.mapToSAP:eigenBeanio.timer?action=stop" />
					<log message="${exchangeProperty.trackingId}: unmarshalling complete" loggingLevel="INFO" />
					<setProperty propertyName="unmarshalledEigenDataFormat">
						<simple>${body}</simple>
					</setProperty>
					<setProperty propertyName="processDate">
						<simple>${date:now:yyyyMMdd}</simple>
					</setProperty>

					<!-- validate contents for SAP file -->
					<process ref="sapFileNameAnalyzer" />
					<process ref="sapLotIdGenerator" />
					<to uri="metrics:timer:ERP.Subscriber.Payment.OPACFileConverter.mapToSAP:opacFileBusinessRulesValidator.timer?action=start" />
					<process ref="opacFileBusinessRulesValidator" />
					<to uri="metrics:timer:ERP.Subscriber.Payment.OPACFileConverter.mapToSAP:opacFileBusinessRulesValidator.timer?action=stop" />
					<log message="${exchangeProperty.trackingId}: business rules validation complete" loggingLevel="INFO" />

					<choice>
						<when>
							<simple>${exchangeProperty.fileNameWithoutExt} != null &amp;&amp; ${exchangeProperty.fileNameWithoutExt} contains 'Declined'</simple>
							<!-- map to SAP format -->
							<to uri="metrics:timer:ERP.Subscriber.Payment.OPACFileConverter.mapToSAP:declinedMapper.timer?action=start" />
							<process ref="declinedMapper" />
							<to uri="metrics:timer:ERP.Subscriber.Payment.OPACFileConverter.mapToSAP:declinedMapper.timer?action=stop" />
							<log message="${exchangeProperty.trackingId}: Mapping from Eigen to SAP declined complete" loggingLevel="INFO" />
						</when>
						<otherwise>
							<to uri="metrics:timer:ERP.Subscriber.Payment.OPACFileConverter.mapToSAP:mapper.timer?action=start" />
							<process ref="mapper" />
							<to uri="metrics:timer:ERP.Subscriber.Payment.OPACFileConverter.mapToSAP:mapper.timer?action=stop" />
							<log message="${exchangeProperty.trackingId}: Mapping from Eigen to SAP complete" loggingLevel="INFO" />
						</otherwise>
					</choice>

					<choice>
						<when>
							<simple>${bodyAs(java.util.List)} != null</simple>
							<!-- marshal -->
							<choice>
								<when>
									<simple>${exchangeProperty.fileNameWithoutExt} != null &amp;&amp; ${exchangeProperty.fileNameWithoutExt} contains 'Declined'</simple>
									<log message="${exchangeProperty.trackingId}: trying to Marshall zzzz" loggingLevel="INFO" />
									<to uri="metrics:timer:ERP.Subscriber.Payment.OPACFileConverter.mapToSAP:sapDeclinedBeanio.timer?action=start" />
									<marshal ref="sapDeclinedBeanio" />
									<to uri="metrics:timer:ERP.Subscriber.Payment.OPACFileConverter.mapToSAP:sapDeclinedBeanio.timer?action=stop" />
									<log message="${exchangeProperty.trackingId}: SAP Declined Marshalling complete" loggingLevel="INFO" />
									<!-- write data file with correlation / process id in file name -->
									<setHeader headerName="CamelFileName">
										<simple>${exchangeProperty.sapLotId}_${exchangeProperty.trackingId}.txt</simple>
									</setHeader>
									<to uri="file://{{sapMapper.declinedOutbox}}?chmod={{sapMapper.outbox.permissions}}" />
									<to uri="file://{{cashAdmin.dropbox}}?chmod={{cashAdmin.dropbox.permissions}}" />
									<log message="${exchangeProperty.trackingId}: Sent result file to SAP" loggingLevel="INFO" />
								</when>
								<otherwise>
									<to uri="metrics:timer:ERP.Subscriber.Payment.OPACFileConverter.mapToSAP:sapBeanio.timer?action=start" />
									<marshal ref="sapBeanio" />
									<to uri="metrics:timer:ERP.Subscriber.Payment.OPACFileConverter.mapToSAP:sapBeanio.timer?action=stop" />
									<log message="${exchangeProperty.trackingId}: SAP Approved Marshalling complete" loggingLevel="INFO" />
									<!-- write data file with correlation / process id in file name -->
									<setHeader headerName="CamelFileName">
										<simple>${exchangeProperty.sapLotId}_${exchangeProperty.trackingId}.txt</simple>
									</setHeader>
									<to uri="file://{{sapMapper.outbox}}?chmod={{sapMapper.outbox.permissions}}" />
									<to uri="file://{{cashAdmin.dropbox}}?chmod={{cashAdmin.dropbox.permissions}}" />
									<log message="${exchangeProperty.trackingId}: Sent result file to SAP" loggingLevel="INFO" />
								</otherwise>
							</choice>
						</when>
						<otherwise>
							<log message="${exchangeProperty.trackingId}: Result file could not be created due to empty mapping." loggingLevel="INFO" />
						</otherwise>
					</choice>

					<setHeader headerName="CamelFileName">
						<simple>${exchangeProperty.fileNameWithoutExt}-${date:now:yyyyMMddHHmmssSSS}.txt</simple>
					</setHeader>
					<to uri="file:{{sapMapper.done}}" />
				</when>
			</choice>
		</route>
		<!-- Delete file -->
		<route id="ERP.Subscriber.Payment.OPACFileConverter.deleteRemoteFile">
			<from uri="direct:deleteRemoteFile" />
			<setProperty propertyName="origFileName">
				<simple>${header.CamelFileName}</simple>
			</setProperty>
			<log message="Deleting remote file: ${exchangeProperty.origFileName}" loggingLevel="INFO" />
			<removeHeaders pattern="CamelHttp*" />
			<removeHeaders pattern="Eigen*" />
			<setBody>
				<simple>${null}</simple>
			</setBody>
			<to uri="token:oauth?clientId={{oauth.esbClientId}}&amp;clientSecret={{oauth.esbClientSecret}}&amp;scope={{oauth.esbScope}}" />
			<setHeader headerName="CamelHttpMethod">
				<constant>DELETE</constant>
			</setHeader>
			<to uri="metrics:timer:ERP.Subscriber.Payment.OPACFileConverter.deleteFile:httpDeleteFile.timer?action=start" />
			<toD uri="{{proxy.file.uri}}${exchangeProperty.origFileName}?synchronous=true&amp;throwExceptionOnFailure=true" />
			<to uri="metrics:timer:ERP.Subscriber.Payment.OPACFileConverter.deleteFile:httpDeleteFile.timer?action=stop" />
		</route>
		<route id="ERP.Subscriber.Payment.OPACFileConverter.dlq">
			<from uri="direct:deadLetterQueue" />
			<log message="{{log.errorTag}}-dlq-${exception.stacktrace}." loggingLevel="ERROR" />
		</route>
	</camelContext>
</beans>
