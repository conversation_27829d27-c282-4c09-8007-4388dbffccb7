spring.application.name=batch-demo

# Spring Batch Configuration
spring.batch.initialize-schema=always
spring.batch.job.enabled=false

# Database Configuration (H2 for Spring Batch metadata)
spring.datasource.url=jdbc:h2:mem:batchdb
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
spring.h2.console.enabled=true

# JPA Configuration
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false

# Batch Processing Directories
batch.input.directory=${java.io.tmpdir}/batch/input
batch.output.directory=${java.io.tmpdir}/batch/output
batch.done.directory=${java.io.tmpdir}/batch/done
batch.error.directory=${java.io.tmpdir}/batch/error

# Duplicate File Check Configuration
batch.duplicate.fileCheck.age=10

# SAP Configuration
sap.client=100

# Logging Configuration
logging.level.org.example.batchdemo=INFO
logging.level.org.springframework.batch=INFO
