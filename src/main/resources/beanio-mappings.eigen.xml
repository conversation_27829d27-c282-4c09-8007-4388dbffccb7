<!-- edited with XMLSpy v2018 rel. 2 sp1 (http://www.altova.com) by <PERSON> (407ETR CONCESSION COMPANY LTD) -->
<beanio xmlns="http://www.beanio.org/2012/03" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.beanio.org/2012/03 http://www.beanio.org/2012/03/mapping.xsd">
	<stream name="eigenData" format="fixedlength" strict="true" mode="readwrite">
		<group name="eigenRootGroup" order="1" minOccurs="1" maxOccurs="1" class="org.example.batchdemo.model.eigen.EigenRootGroup">
			<record name="fileHeader" order="1" minOccurs="1" maxOccurs="1" minLength="39" maxLength="39" class="org.example.batchdemo.model.eigen.EigenFileHeader">
				<field name="recordType" length="1" rid="true" literal="F" required="true"/>
				<field name="submissionDate" length="8" format="MMddyyyy" required="true"/>
				<field name="processedDate" length="8" format="MMddyyyy" required="true"/>
				<field name="ofSeqNumber" length="3" required="false" align="right" padding=" "/>
				<field name="efSeqNumber" length="3" required="false" align="right" padding=" "/>
				<field name="uid" length="16" required="true"/>
			</record>
			<group name="batches" order="2" minOccurs="1" collection="list" class="org.example.batchdemo.model.eigen.EigenBatch">
				<record name="batchHeader" order="1" minOccurs="1" maxOccurs="1" minLength="61" maxLength="61" class="org.example.batchdemo.model.eigen.EigenBatchHeader">
					<field name="recordType" length="1" rid="true" literal="B" required="true"/>
					<field name="processedDate" length="8" format="MMddyyyy" required="true"/>
					<field name="obSeqNumber" length="3" required="false" align="right" padding=" "/>
					<field name="ebSeqNumber" length="3" required="false" align="right" padding=" "/>
					<field name="batchDCode" length="6" required="false"/>
					<field name="echoDataBatch" length="40" required="false"/>
				</record>
				<record name="batchDetails" order="2" minOccurs="1" minLength="236" maxLength="236" collection="list" class="org.example.batchdemo.model.eigen.EigenBatchDetail">
					<field name="recordType" length="1" rid="true" literal="D" required="true"/>
					<field name="odSeqNumber" length="8" required="false" align="right" padding=" "/>
					<field name="processedCode" length="4" required="false"/>
					<field name="errorCode" length="4" required="false"/>
					<field name="errorField" length="20" required="false"/>
					<field name="errorDesc" length="20" required="false"/>
					<field name="responseCode" length="3" required="false"/>
					<field name="eiSeqNumber" length="8" required="false" align="right" padding=" "/>
					<field name="termId" length="16" required="true"/>
					<field name="transCode" length="2" required="false"/>
					<field name="track2Acc" length="40" required="true"/>
					<field name="amount" length="9" required="true" align="right" padding=" "/>
					<field name="amount2" length="9" required="false" align="right" padding=" "/>
					<field name="approvalCd" length="8" required="false"/>
					<field name="opId" length="2" required="false"/>
					<field name="invoiceNum" length="10" required="false"/>
					<field name="extOpId" length="16" required="true"/>
					<field name="clientId" length="16" required="false"/>
					<field name="custId" length="12" required="true" align="left" padding=" "/>
					<field name="paymentType" length="1" required="false"/>
					<field name="amount3" length="8" required="false" align="right" padding="0"/>
					<field name="taxAmount" length="8" required="true" align="right" padding="0"/>
					<field name="paymentSource" length="1" required="true"/>
					<field name="referenceNumber" length="10" required="false"/>
				</record>
				<record name="batchTrailer" order="3" minOccurs="1" maxOccurs="1" minLength="31" maxLength="31" class="org.example.batchdemo.model.eigen.EigenBatchTrailer">
					<field name="recordType" length="1" rid="true" literal="T" required="true"/>
					<field name="obSeqNumber" length="3" required="true" align="right" padding=" "/>
					<field name="ebSeqNumber" length="3" required="true" align="right" padding=" "/>
					<field name="totalDetailLinesInBatch" length="8" required="true" align="right" padding="0"/>
					<field name="totalBatchAmount" length="16" required="true" align="right" padding="0"/>
				</record>
			</group>
			<record name="fileTrailer" order="3" minOccurs="1" maxOccurs="1" minLength="35" maxLength="35" class="org.example.batchdemo.model.eigen.EigenFileTrailer">
				<field name="recordType" length="1" rid="true" literal="R" required="true"/>
				<field name="ofSeqNumber" length="3" required="true"  align="right" padding=" "/>
				<field name="efSeqNumber" length="3" required="true"  align="right" padding=" "/>
				<field name="numberOfBatches" length="4" required="true"  align="right" padding="0"/>
				<field name="totalDetailLinesInFile" length="8" required="true"  align="right" padding="0"/>
				<field name="totalFileAmount" length="16" required="true" align="right" padding="0"/>
			</record>
		</group>
	</stream>
</beanio>
