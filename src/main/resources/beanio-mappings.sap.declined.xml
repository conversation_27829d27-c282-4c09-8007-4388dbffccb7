<!-- edited with XMLSpy v2017 sp2 (http://www.altova.com) by <PERSON> (407ETR CONCESSION COMPANY LTD) -->
<beanio xmlns="http://www.beanio.org/2012/03" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.beanio.org/2012/03 http://www.beanio.org/2012/03/mapping.xsd">
	<stream name="SAPDeclinedData" format="fixedlength" strict="true" mode="write">
		<parser>
			<property name="recordTerminator" value="\n"/>
		</parser>
		<group name="rootGroup" minOccurs="1" maxOccurs="1" order="1" class="com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.sap.declined.SAPDeclinedRootGroup">
			<record name="BFKKZGR00" class="com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.sap.declined.BFKKZGR00" order="1" minLength="5" maxLength="5" minOccurs="1" maxOccurs="1">
				<field name="STYPE" length="1" rid="true" literal="0" required="true"/>
				<field name="MANDT" length="3" required="true"/>
				<field name="APPLK" length="1" required="true" literal="S"/>
			</record>
			<record name="BFKKRK" class="com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.sap.declined.BFKKRK" order="2" minLength="257" maxLength="257" minOccurs="1" maxOccurs="1">
				<field name="STYPE" length="1" rid="true" literal="1" required="true"/>
				<field name="TBNAM" length="30" required="true" literal="BFKKRK"/>
				<field name="KEYR1" length="12" required="true"/>
				<field name="KEYR2" length="40" required="true" literal="PAC Declines"/>
				<field name="FIKEY" length="12" required="true"/>
				<field name="RLSKO" length="10" required="true" literal="0000011124"/>
				<field name="BUKRS" length="4" required="true" literal="407"/>
				<field name="GSBER" length="4" required="true" literal="ETR"/>
				<field name="BLART" length="2" required="true" literal="RT"/>
				<field name="WAERS" length="5" required="true" literal="CAD"/>
				<field name="KURSF" length="10" required="true"/>
				<field name="BUDAT" length="8" required="true" format="yyyyMMdd"/>
				<field name="BLDAT" length="8" required="true" format="yyyyMMdd"/>
				<field name="VALUT" length="8" required="true" format="yyyyMMdd"/>
				<field name="XEIPH" length="1" required="true"/>
				<field name="AUGRD" length="2" required="true" literal="10"/>
				<field name="XRLSD" length="1" literal="X" required="true"/>
				<field name="XRLSK" length="1" required="true"/>
				<field name="XSTEB" length="1" required="true"/>
				<field name="XRLSB" length="1" required="true"/>
				<field name="BANKL" length="3" required="true"/>
				<field name="BANKK" length="15" required="true"/>
				<field name="BANKN" length="18" required="true"/>
				<field name="HBKID" length="5" required="true"/>
				<field name="HKTID" length="5" required="true"/>
				<field name="XCALCGEB" length="1" required="true" literal="X"/>
				<field name="XACCEPTCHARGES" length="1" required="true"/>
				<field name="RLMOD" length="1" required="true"/>
				<field name="KSUMS" length="15" required="true"/>
				<field name="KSUMH" length="15" required="true" type="java.math.BigDecimal"/>
				<field name="KSUMP" length="6" required="true" align="right" padding="0"/>
				<field name="XERWR" length="1" required="true" literal="X"/>
				<field name="PRCTR" length="10" required="true"/>
			</record>
			<record name="BFKKRPList" class="com.etr407.erp.services.esb.subscriber.payment.opacfileconverter.datamodel.sap.declined.BFKKRP" order="3" minLength="502" maxLength="502" minOccurs="1" collection="list">
				<field name="STYPE" length="1" rid="true" literal="2" required="true"/>
				<field name="TBNAM" length="30" required="true" literal="BFKKRP"/>
				<field name="SELT1" length="1" literal="U" required="true"/>
				<field name="SELW1" length="20" required="true"/>
				<field name="BETRR" length="16" required="true" type="java.math.BigDecimal"/>
				<field name="BETRH" length="16" required="true"/>
				<field name="BTRB1" length="16" required="true"/>
				<field name="BTRB2" length="16" required="true"/>
				<field name="BTRV1" length="16" required="true"/>
				<field name="BTRV2" length="16" required="true"/>
				<field name="STBB1" length="16" required="true"/>
				<field name="STBB2" length="16" required="true"/>
				<field name="STBV1" length="16" required="true"/>
				<field name="STBV2" length="16" required="true"/>
				<field name="SKZB1" length="2" required="true"/>
				<field name="SKZB2" length="2" required="true"/>
				<field name="SKZV1" length="2" required="true"/>
				<field name="SKZV2" length="2" required="true"/>
				<field name="RLSKO" length="10" required="true"/>
				<field name="BUKRS" length="4" literal="407" required="true"/>
				<field name="GSBER" length="4" required="true" literal="ETR"/>
				<field name="BLART" length="2" literal="RT" required="true"/>
				<field name="WAERS" length="5" literal="CAD" required="true"/>
				<field name="KURSF" length="10" required="true"/>
				<field name="BUDAT" length="8" required="true" format="yyyyMMdd"/>
				<field name="BLDAT" length="8" required="true" format="yyyyMMdd"/>
				<field name="VALUT" length="8" required="true" format="yyyyMMdd"/>
				<field name="XEIPH" length="1" required="true"/>
				<field name="RLGRD" length="3" required="true"/>
				<field name="RLHBK" length="6" required="true"/>
				<field name="TXTVW" length="80" required="true"/>
				<field name="BANKL" length="3" required="true"/>
				<field name="BANKK" length="15" required="true"/>
				<field name="BANKN" length="18" required="true"/>
				<field name="IBAN" length="34" required="true"/>
				<field name="CHECF" length="16" required="true"/>
				<field name="XACCEPTCHARGES" length="1" required="true"/>
				<field name="HBKID" length="5" required="true"/>
				<field name="HKTID" length="5" required="true"/>
				<field name="RLMOD" length="1" required="true"/>
				<field name="XERWR" length="1" required="true"/>
				<field name="PRCTR" length="10" required="true"/>
				<field name="KUKEY" length="8" required="true"/>
				<field name="ESNUM" length="5" required="true"/>
				<field name="SWIFT" length="11" required="true"/>
			</record>
		</group>
	</stream>
</beanio>
