# OPAC File Processing - Spring Batch Application

This application has been converted from Apache Camel to Spring Batch for processing OPAC payment files.

## Overview

The application processes OPAC payment files in Eigen format, validates them, routes payments by type (Amex, Mastercard, Visa, Declined), and converts them to SAP format.

## Architecture

### Package Structure
```
org.example.batchdemo
├── model.eigen          # Data models for Eigen format
├── batch               # Spring Batch components
├── config              # Batch configuration
├── controller          # REST endpoints
├── service             # Business services
├── exception           # Custom exceptions
└── util                # Utility classes
```

### Key Components

#### Data Models (`org.example.batchdemo.model.eigen`)
- `EigenRootGroup` - Root container for file data
- `EigenFileHeader/Trailer` - File-level metadata
- `EigenBatch` - Batch container
- `EigenBatchHeader/Trailer` - Batch-level metadata
- `EigenBatchDetail` - Individual payment records

#### Batch Components (`org.example.batchdemo.batch`)
- `EigenFileReader` - Reads Eigen format files using BeanIO
- `ValidationProcessor` - Validates business rules and file integrity
- `PaymentRouter` - Routes payments by type (AX, MC, VI, DECLINED)
- `PaymentProcessor` - Main processing orchestrator
- `DuplicateFileChecker` - Prevents duplicate file processing

#### Configuration (`org.example.batchdemo.config`)
- `BatchConfiguration` - Spring Batch job and step definitions

#### REST API (`org.example.batchdemo.controller`)
- `FileProcessingController` - Provides REST endpoints for triggering processing

## REST Endpoints

### POST `/api/opac/fetch`
Initiates file fetching process.

### POST `/api/opac/router`
Starts the main file processing job.

### POST `/api/opac/validate`
Triggers file validation.

## Configuration

### Application Properties
```properties
# Spring Batch Configuration
spring.batch.jdbc.initialize-schema=always
spring.batch.job.enabled=false

# Database Configuration (H2 for Spring Batch metadata)
spring.datasource.url=jdbc:h2:mem:batchdb

# Batch Processing Directories
batch.input.directory=${java.io.tmpdir}/batch/input
batch.output.directory=${java.io.tmpdir}/batch/output
batch.done.directory=${java.io.tmpdir}/batch/done
batch.error.directory=${java.io.tmpdir}/batch/error

# Duplicate File Check Configuration
batch.duplicate.fileCheck.age=10
```

## Key Features

### 1. File Processing Pipeline
- **Read**: BeanIO-based reading of fixed-length Eigen format files
- **Validate**: Business rules validation including totals and counts
- **Route**: Payment routing by card type and status
- **Process**: Data transformation and mapping

### 2. Business Rules Validation
- File structure validation (header, batches, trailer)
- Batch count and amount validation
- Detail record count validation
- Cross-field validation

### 3. Payment Routing
Payments are routed based on:
- **Amex (AX)**: Payment type 'A'
- **Mastercard (MC)**: Payment type 'M'
- **Visa (VI)**: Payment type 'V'
- **Declined**: Error codes or failed response codes

### 4. Duplicate Detection
- Checks against archived files in done directory
- Configurable age limit for duplicate checking
- Filename and sequence number validation

## Running the Application

### Prerequisites
- Java 17
- Maven 3.6+

### Build and Run
```bash
mvn clean compile
mvn test
mvn spring-boot:run -Dspring-boot.run.arguments=--server.port=8081
```

### Testing Endpoints
```bash
# Test file fetch
curl -X POST http://localhost:8081/api/opac/fetch

# Test file validation
curl -X POST http://localhost:8081/api/opac/validate

# Test file processing
curl -X POST http://localhost:8081/api/opac/router
```

## Migration from Camel

### What Changed
1. **Framework**: Apache Camel → Spring Batch
2. **Processing Model**: Route-based → Job/Step-based
3. **Configuration**: XML routes → Java configuration
4. **Package Structure**: `com.etr407.*` → `org.example.batchdemo.*`

### What Remained
1. **Data Models**: Same structure, updated package names
2. **BeanIO Mappings**: Same file format parsing
3. **Business Logic**: Same validation and routing rules
4. **REST Endpoints**: Same API contract

### Benefits of Spring Batch
1. **Better Error Handling**: Built-in retry and skip mechanisms
2. **Monitoring**: Job execution tracking and metrics
3. **Scalability**: Chunk-based processing and partitioning
4. **Transaction Management**: Robust transaction handling
5. **Restart Capability**: Job restart from failure points

## Database

The application uses H2 in-memory database for Spring Batch metadata tables:
- `BATCH_JOB_INSTANCE`
- `BATCH_JOB_EXECUTION`
- `BATCH_STEP_EXECUTION`
- etc.

Access H2 console at: http://localhost:8081/h2-console
- JDBC URL: `jdbc:h2:mem:batchdb`
- Username: `sa`
- Password: (empty)

## Future Enhancements

1. **File Writers**: Implement SAP format file writers
2. **Scheduling**: Add scheduled file processing
3. **Monitoring**: Add metrics and health checks
4. **Error Handling**: Enhanced error reporting and recovery
5. **Testing**: Add integration tests with sample files
